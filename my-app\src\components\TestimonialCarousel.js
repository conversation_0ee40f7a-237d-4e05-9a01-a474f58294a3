"use client"
import React, { useState, useEffect } from 'react';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Content Creator",
    company: "Digital Media Co.",
    avatar: "SJ",
    content: "Kambaa AI has revolutionized my workflow. I can transcribe hours of interviews in minutes with incredible accuracy. It's a game-changer for content creators.",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Podcast Host",
    company: "Tech Talk Podcast",
    avatar: "MC",
    content: "The accuracy is phenomenal! I've tried many transcription services, but Kambaa AI consistently delivers 99%+ accuracy even with technical jargon.",
    rating: 5
  },
  {
    id: 3,
    name: "Dr. <PERSON>",
    role: "Medical Researcher",
    company: "Stanford Medical",
    avatar: "ER",
    content: "Perfect for transcribing medical interviews and research sessions. The multi-language support and speaker identification features are outstanding.",
    rating: 5
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Business Analyst",
    company: "Fortune 500 Corp",
    avatar: "JW",
    content: "We use Kambaa AI for all our meeting transcriptions. The real-time processing and export options have improved our team's productivity by 300%.",
    rating: 5
  },
  {
    id: 5,
    name: "<PERSON>",
    role: "Journalist",
    company: "News Network",
    avatar: "L<PERSON>",
    content: "As a journalist, accuracy and speed are crucial. Kambaa AI delivers both flawlessly. The noise reduction feature works perfectly even in busy environments.",
    rating: 5
  },
  {
    id: 6,
    name: "David Park",
    role: "Student",
    company: "MIT",
    avatar: "DP",
    content: "This tool has been invaluable for transcribing lectures and study sessions. The affordable pricing makes it accessible for students like me.",
    rating: 5
  }
];

export default function TestimonialCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToSlide = (index) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds
  };

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  return (
    <section className="w-full py-24 px-4"style={{background: 'linear-gradient(135deg, var(--primary-50), var(--secondary-50))'}}>
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full mb-6 border" style={{backgroundColor: 'var(--primary-100)', borderColor: 'var(--primary-200)', color: 'var(--primary-700)'}}>
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span className="text-sm font-semibold uppercase tracking-wider">Testimonials</span>
          </div>
          
          <h2 className="font-bold mb-4 leading-tight" style={{fontSize: 'var(--text-4xl)', color: 'var(--neutral-900)'}}>
            What Our Users Say About{' '}
            <span style={{color: 'var(--primary-600)'}}>Kambaa AI</span>
          </h2>
          
          <p className="max-w-2xl mx-auto leading-relaxed" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-600)'}}>
            Join thousands of satisfied users who have transformed their workflow with our AI-powered transcription service.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Main Testimonial Display */}
          <div className="bg-white rounded-3xl shadow-xl p-8 md:p-12 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full blur-2xl opacity-10" style={{backgroundColor: 'var(--primary-500)'}}></div>
            
            {/* Quote icon */}
            <div className="absolute top-8 left-8 opacity-10">
              <svg className="w-16 h-16" style={{color: 'var(--primary-500)'}} viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z" />
              </svg>
            </div>

            <div className="relative z-10">
              {/* Stars */}
              <div className="flex gap-1 mb-6">
                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                  <svg key={i} className="w-5 h-5" style={{color: 'var(--secondary-500)'}} viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                ))}
              </div>

              {/* Testimonial Content */}
              <blockquote className="mb-8 leading-relaxed" style={{fontSize: 'var(--text-xl)', color: 'var(--neutral-700)'}}>
                "{testimonials[currentIndex].content}"
              </blockquote>

              {/* Author Info */}
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full flex items-center justify-center font-bold text-white" style={{backgroundColor: 'var(--primary-600)'}}>
                  {testimonials[currentIndex].avatar}
                </div>
                <div>
                  <div className="font-semibold" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-900)'}}>
                    {testimonials[currentIndex].name}
                  </div>
                  <div style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-600)'}}>
                    {testimonials[currentIndex].role} at {testimonials[currentIndex].company}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
            style={{backgroundColor: 'white', color: 'var(--primary-600)'}}
          >
            <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M15 18l-6-6 6-6"/>
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110"
            style={{backgroundColor: 'white', color: 'var(--primary-600)'}}
          >
            <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 18l6-6-6-6"/>
            </svg>
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center gap-3 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex ? 'scale-125' : 'hover:scale-110'
              }`}
              style={{
                backgroundColor: index === currentIndex ? 'var(--primary-600)' : 'var(--neutral-300)'
              }}
            />
          ))}
        </div>

        {/* Auto-play indicator */}
        <div className="text-center mt-4">
          <button
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            className="text-sm px-4 py-2 rounded-full transition-colors duration-300"
            style={{color: 'var(--neutral-600)'}}
          >
            {isAutoPlaying ? '⏸️ Pause' : '▶️ Play'} Auto-scroll
          </button>
        </div>
      </div>
    </section>
  );
}
