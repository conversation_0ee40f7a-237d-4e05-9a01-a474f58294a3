{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Navbar.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\nexport default function Navbar() {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [lastScrollY, setLastScrollY] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const controlNavbar = () => {\r\n      if (typeof window !== 'undefined') {\r\n        if (window.scrollY > lastScrollY && window.scrollY > 100) {\r\n          // Scrolling down & past 100px\r\n          setIsVisible(false);\r\n        } else {\r\n          // Scrolling up\r\n          setIsVisible(true);\r\n        }\r\n        setLastScrollY(window.scrollY);\r\n      }\r\n    };\r\n\r\n    if (typeof window !== 'undefined') {\r\n      window.addEventListener('scroll', controlNavbar);\r\n      return () => {\r\n        window.removeEventListener('scroll', controlNavbar);\r\n      };\r\n    }\r\n  }, [lastScrollY]);\r\n\r\n  return (\r\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ${\r\n      isVisible ? 'translate-y-0' : '-translate-y-full'\r\n    } bg-black/90 backdrop-blur-sm border-b border-gray-800/50`}>\r\n      <div className=\"max-w-7xl mx-auto flex items-center justify-between py-4 px-6\">\r\n        {/* Logo */}\r\n        <div className=\"flex items-center gap-2 text-white font-bold text-xl\">\r\n          ZINLE\r\n        </div>\r\n\r\n        {/* Navigation Links */}\r\n        <div className=\"hidden md:flex items-center gap-8 text-gray-300 font-medium text-sm\">\r\n          <a href=\"#\" className=\"hover:text-white transition-colors duration-200\">Home</a>\r\n          <a href=\"#\" className=\"hover:text-white transition-colors duration-200\">Studio</a>\r\n          <a href=\"#\" className=\"hover:text-white transition-colors duration-200\">Works</a>\r\n          <a href=\"#\" className=\"hover:text-white transition-colors duration-200\">Services</a>\r\n          <a href=\"#\" className=\"hover:text-white transition-colors duration-200\">Blogs</a>\r\n        </div>\r\n\r\n        {/* CTA Button */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <button className=\"px-6 py-2 text-white font-medium text-sm hover:text-gray-300 transition-colors duration-200 flex items-center gap-2\">\r\n            Work with us\r\n            <svg className=\"w-4 h-4\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <path d=\"M7 17L17 7\"/>\r\n              <path d=\"M7 7h10v10\"/>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Mobile menu button */}\r\n        <button className=\"md:hidden text-white\">\r\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n          </svg>\r\n        </button>\r\n      </div>\r\n    </nav>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;kDAAgB;oBACpB,wCAAmC;wBACjC,IAAI,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO,GAAG,KAAK;4BACxD,8BAA8B;4BAC9B,aAAa;wBACf,OAAO;4BACL,eAAe;4BACf,aAAa;wBACf;wBACA,eAAe,OAAO,OAAO;oBAC/B;gBACF;;YAEA,wCAAmC;gBACjC,OAAO,gBAAgB,CAAC,UAAU;gBAClC;wCAAO;wBACL,OAAO,mBAAmB,CAAC,UAAU;oBACvC;;YACF;QACF;2BAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,WAAW,AAAC,qEAEhB,OADC,YAAY,kBAAkB,qBAC/B;kBACC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BAAuD;;;;;;8BAKtE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAkD;;;;;;sCACxE,6LAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAkD;;;;;;sCACxE,6LAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAkD;;;;;;sCACxE,6LAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAkD;;;;;;sCACxE,6LAAC;4BAAE,MAAK;4BAAI,WAAU;sCAAkD;;;;;;;;;;;;8BAI1E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAO,WAAU;;4BAAsH;0CAEtI,6LAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;gCAAO,QAAO;gCAAe,aAAY;;kDACzF,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;8BAMd,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;GAjEwB;KAAA", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyAnalyticsSection.js"], "sourcesContent": ["\"use client\"\nimport React, { useRef, useEffect, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\ngsap.registerPlugin(ScrollTrigger);\n\nconst analyticsFeatures = [\n  {\n    id: 1,\n    title: \"Real-Time Speech Recognition\",\n    description: \"Experience instant voice-to-text conversion with our advanced AI. Perfect for live meetings, interviews, and lectures with zero delay.\",\n    icon: \"🎤\",\n    bgColor: \"bg-blue-50\",\n    iconBg: \"bg-blue-100\"\n  },\n  {\n    id: 2,\n    title: \"Multi-Language Support\",\n    description: \"Break language barriers with support for 50+ languages and dialects. Our AI adapts to accents and speaking styles automatically.\",\n    icon: \"🌍\",\n    bgColor: \"bg-green-50\",\n    iconBg: \"bg-green-100\"\n  },\n  {\n    id: 3,\n    title: \"Smart Audio Enhancement\",\n    description: \"Advanced noise reduction and audio clarity enhancement ensure perfect transcription even in challenging environments.\",\n    icon: \"🔊\",\n    bgColor: \"bg-purple-50\",\n    iconBg: \"bg-purple-100\"\n  },\n  {\n    id: 4,\n    title: \"Export & Integration\",\n    description: \"Export transcripts in multiple formats (TXT, DOCX, SRT, VTT) and integrate seamlessly with your favorite productivity tools.\",\n    icon: \"📄\",\n    bgColor: \"bg-orange-50\",\n    iconBg: \"bg-orange-100\"\n  }\n];\n\nexport default function StickyAnalyticsSection() {\n  const [activeIdx, setActiveIdx] = useState(0);\n  const contentRefs = useRef([]);\n  const sectionRef = useRef();\n\n  useEffect(() => {\n    contentRefs.current.forEach((el, idx) => {\n      if (!el) return;\n      \n      ScrollTrigger.create({\n        trigger: el,\n        start: \"top center\",\n        end: \"bottom center\",\n        onEnter: () => setActiveIdx(idx),\n        onEnterBack: () => setActiveIdx(idx),\n      });\n    });\n\n    return () => {\n      ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n    };\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"w-full py-24 px-4 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Left side - Sticky content */}\n          <div className=\"lg:sticky lg:top-32 lg:h-fit\">\n            <h2 style={{fontSize: 'var(--text-4xl)', color: 'var(--neutral-900)'}} className=\"font-bold mb-6 leading-tight\">\n              We make audio transcription effortless\n            </h2>\n            <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-600)'}} className=\"mb-8 leading-relaxed\">\n              Transform your audio into accurate text with our AI-powered transcription service. Simple, fast, and reliable - perfect for professionals, students, and content creators.\n            </p>\n            <button className=\"btn-primary\">\n              Start Transcribing\n            </button>\n          </div>\n          \n          {/* Right side - Scrollable feature boxes */}\n          <div className=\"space-y-8\">\n            {analyticsFeatures.map((feature, idx) => (\n              <div\n                key={feature.id}\n                ref={el => (contentRefs.current[idx] = el)}\n                className={`${feature.bgColor} rounded-2xl p-8 border-2 transition-all duration-500 ${\n                  activeIdx === idx ? 'border-gray-300 shadow-lg scale-105' : 'border-transparent'\n                }`}\n                style={{ minHeight: '300px' }}\n              >\n                <div className=\"flex items-start gap-6\">\n                  <div className={`${feature.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center text-2xl flex-shrink-0`}>\n                    {feature.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 style={{fontSize: 'var(--text-2xl)', color: 'var(--neutral-900)'}} className=\"font-bold mb-4\">\n                      {feature.title}\n                    </h3>\n                    <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-700)'}} className=\"leading-relaxed\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n                \n                {/* Visual representation based on feature */}\n                <div className=\"mt-8\">\n                  {idx === 0 && (\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"relative\">\n                        <div className=\"w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-lg\">\n                          <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center\">\n                            <span className=\"text-white text-2xl\">🎤</span>\n                          </div>\n                        </div>\n                        <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full animate-pulse\"></div>\n                        <div className=\"absolute -bottom-2 -left-2 w-6 h-6 bg-blue-300 rounded-full animate-pulse delay-100\"></div>\n                        <div className=\"absolute top-8 -left-8 w-4 h-4 bg-blue-200 rounded-full animate-pulse delay-200\"></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 1 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"flex items-center justify-center gap-2 mb-2\">\n                        <span className=\"text-sm text-gray-600\">🇺🇸 🇪🇸 🇫🇷 🇩🇪 🇯🇵</span>\n                      </div>\n                      <div className=\"flex items-end justify-between h-16\">\n                        <div className=\"w-6 bg-green-300 rounded-t\" style={{height: '40%'}}></div>\n                        <div className=\"w-6 bg-green-400 rounded-t\" style={{height: '60%'}}></div>\n                        <div className=\"w-6 bg-green-500 rounded-t\" style={{height: '80%'}}></div>\n                        <div className=\"w-6 bg-green-600 rounded-t\" style={{height: '100%'}}></div>\n                        <div className=\"w-6 bg-green-400 rounded-t\" style={{height: '70%'}}></div>\n                        <div className=\"w-6 bg-green-500 rounded-t\" style={{height: '85%'}}></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 2 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"flex items-center justify-center mb-4\">\n                        <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center\">\n                          <span className=\"text-2xl\">🔊</span>\n                        </div>\n                      </div>\n                      <div className=\"flex items-end justify-center gap-1 h-12\">\n                        <div className=\"w-2 bg-purple-300 rounded-t\" style={{height: '30%'}}></div>\n                        <div className=\"w-2 bg-purple-500 rounded-t\" style={{height: '70%'}}></div>\n                        <div className=\"w-2 bg-purple-600 rounded-t\" style={{height: '100%'}}></div>\n                        <div className=\"w-2 bg-purple-400 rounded-t\" style={{height: '50%'}}></div>\n                        <div className=\"w-2 bg-purple-500 rounded-t\" style={{height: '80%'}}></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 3 && (\n                    <div className=\"flex justify-center gap-3\">\n                      <div className=\"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">TXT</div>\n                      <div className=\"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">DOC</div>\n                      <div className=\"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">SRT</div>\n                      <div className=\"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">VTT</div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,YAAY,OAAO,CAAC,OAAO;oDAAC,CAAC,IAAI;oBAC/B,IAAI,CAAC,IAAI;oBAET,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS;wBACT,OAAO;wBACP,KAAK;wBACL,OAAO;gEAAE,IAAM,aAAa;;wBAC5B,WAAW;gEAAE,IAAM,aAAa;;oBAClC;gBACF;;YAEA;oDAAO;oBACL,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;4DAAC,CAAA,UAAW,QAAQ,IAAI;;gBACxD;;QACF;2CAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,OAAO;oCAAC,UAAU;oCAAmB,OAAO;gCAAoB;gCAAG,WAAU;0CAA+B;;;;;;0CAGhH,6LAAC;gCAAE,OAAO;oCAAC,UAAU;oCAAkB,OAAO;gCAAoB;gCAAG,WAAU;0CAAuB;;;;;;0CAGtG,6LAAC;gCAAO,WAAU;0CAAc;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,oBAC/B,6LAAC;gCAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;gCACvC,WAAW,AAAC,GACV,OADY,QAAQ,OAAO,EAAC,0DAE7B,OADC,cAAc,MAAM,wCAAwC;gCAE9D,OAAO;oCAAE,WAAW;gCAAQ;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,GAAiB,OAAf,QAAQ,MAAM,EAAC;0DAC/B,QAAQ,IAAI;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,OAAO;4DAAC,UAAU;4DAAmB,OAAO;wDAAoB;wDAAG,WAAU;kEAC9E,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,OAAO;4DAAC,UAAU;4DAAkB,OAAO;wDAAoB;wDAAG,WAAU;kEAC5E,QAAQ,WAAW;;;;;;;;;;;;;;;;;;kDAM1B,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,mBACP,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;4CAKpB,QAAQ,mBACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAM;;;;;;0EAClE,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,6LAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;;;;;;;;;;;;;4CAKtE,QAAQ,mBACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;kEAG/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,6LAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,6LAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAM;;;;;;0EACnE,6LAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,6LAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;;;;;;;;;;;;;4CAKvE,QAAQ,mBACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmG;;;;;;kEAClH,6LAAC;wDAAI,WAAU;kEAAiG;;;;;;kEAChH,6LAAC;wDAAI,WAAU;kEAAkG;;;;;;kEACjH,6LAAC;wDAAI,WAAU;kEAAmG;;;;;;;;;;;;;;;;;;;+BA5EnH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAuF/B;GAnIwB;KAAA", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/InteractiveNotificationSection.js"], "sourcesContent": ["\"use client\"\nimport React, { useState, useEffect } from 'react';\n\nconst notifications = [\n  {\n    id: 1,\n    type: 'success',\n    icon: '🎤',\n    title: 'Audio transcription completed',\n    message: 'Your 45-minute meeting has been transcribed with 99.8% accuracy',\n    position: { top: '15%', left: '5%' },\n    delay: 0\n  },\n  {\n    id: 2,\n    type: 'info',\n    icon: '📊',\n    title: 'New transcription request',\n    message: 'Processing audio file: \"Team_Meeting_Jan_2024.mp3\"',\n    position: { top: '25%', right: '8%' },\n    delay: 1000\n  },\n  {\n    id: 3,\n    type: 'success',\n    icon: '✨',\n    title: 'AI Enhancement Complete',\n    message: 'Speaker identification and punctuation added automatically',\n    position: { bottom: '30%', left: '10%' },\n    delay: 2000\n  },\n  {\n    id: 4,\n    type: 'notification',\n    icon: '📝',\n    title: 'Export Ready',\n    message: 'Your transcript is ready for download in multiple formats',\n    position: { bottom: '15%', right: '12%' },\n    delay: 3000\n  }\n];\n\nexport default function InteractiveNotificationSection() {\n  const [visibleNotifications, setVisibleNotifications] = useState([]);\n\n  useEffect(() => {\n    notifications.forEach((notification) => {\n      setTimeout(() => {\n        setVisibleNotifications(prev => [...prev, notification.id]);\n      }, notification.delay);\n    });\n  }, []);\n\n  const getNotificationStyle = (notification) => {\n    const baseStyle = {\n      position: 'absolute',\n      transform: visibleNotifications.includes(notification.id) \n        ? 'translateY(0) scale(1)' \n        : 'translateY(20px) scale(0.9)',\n      opacity: visibleNotifications.includes(notification.id) ? 1 : 0,\n      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',\n      ...notification.position\n    };\n    return baseStyle;\n  };\n\n  return (\n    <section className=\"w-full py-20 px-4 overflow-hidden\" style={{background: 'linear-gradient(135deg, var(--primary-50), var(--secondary-50))'}}>\n      <div className=\"max-w-7xl mx-auto relative\">\n        <div className=\"flex items-center justify-center min-h-[600px]\">\n          {/* Central image container */}\n          <div className=\"relative\">\n            {/* Main background shape */}\n            <div className=\"w-96 h-96 rounded-3xl transform rotate-6 shadow-2xl\" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}></div>\n\n            {/* Person with laptop image */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div className=\"w-80 h-80 rounded-2xl flex items-center justify-center transform -rotate-6\" style={{background: 'linear-gradient(135deg, var(--primary-400), var(--secondary-400))'}}>\n                {/* Person illustration */}\n                <div className=\"relative\">\n                  {/* Laptop */}\n                  <div className=\"w-32 h-20 bg-gray-800 rounded-lg mb-4 relative\">\n                    <div className=\"w-30 h-18 bg-gray-100 rounded-sm m-1 flex items-center justify-center\">\n                      <div className=\"text-xs text-gray-600\">🎵 → 📝</div>\n                    </div>\n                  </div>\n                  \n                  {/* Person silhouette */}\n                  <div className=\"w-16 h-16 bg-gray-700 rounded-full mx-auto mb-2\"></div>\n                  <div className=\"w-20 h-12 bg-gray-600 rounded-t-full mx-auto\"></div>\n                  \n                  {/* Headphones */}\n                  <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2\">\n                    <div className=\"w-20 h-4 border-4 border-gray-800 rounded-full\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Floating notification cards */}\n        {notifications.map((notification) => (\n          <div\n            key={notification.id}\n            style={getNotificationStyle(notification)}\n            className=\"bg-white rounded-xl shadow-lg p-4 max-w-xs border border-gray-100 hover:shadow-xl transition-shadow duration-300\"\n          >\n            <div className=\"flex items-start gap-3\">\n              <div className=\"w-10 h-10 rounded-lg flex items-center justify-center text-white flex-shrink-0\" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}>\n                <span className=\"text-lg\">{notification.icon}</span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h4 style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-900)'}} className=\"font-semibold mb-1 truncate\">\n                  {notification.title}\n                </h4>\n                <p style={{fontSize: 'var(--text-xs)', color: 'var(--neutral-600)'}} className=\"leading-relaxed\">\n                  {notification.message}\n                </p>\n              </div>\n            </div>\n            \n            {/* Status indicator */}\n            <div className=\"flex items-center justify-between mt-3\">\n              <div className=\"flex items-center gap-1\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500\">Live</span>\n              </div>\n              <span className=\"text-xs text-gray-400\">Just now</span>\n            </div>\n          </div>\n        ))}\n        \n        {/* Background decorative elements */}\n        <div className=\"absolute top-10 left-10 w-20 h-20 bg-orange-200 rounded-full opacity-50 animate-pulse\"></div>\n        <div className=\"absolute bottom-10 right-10 w-16 h-16 bg-yellow-200 rounded-full opacity-50 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-5 w-12 h-12 bg-orange-300 rounded-full opacity-30 animate-bounce\"></div>\n        <div className=\"absolute top-1/3 right-5 w-8 h-8 bg-yellow-300 rounded-full opacity-40 animate-bounce delay-500\"></div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,KAAK;YAAO,MAAM;QAAK;QACnC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,KAAK;YAAO,OAAO;QAAK;QACpC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,QAAQ;YAAO,MAAM;QAAM;QACvC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,QAAQ;YAAO,OAAO;QAAM;QACxC,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oDAAE;YACR,cAAc,OAAO;4DAAC,CAAC;oBACrB;oEAAW;4BACT;4EAAwB,CAAA,OAAQ;2CAAI;wCAAM,aAAa,EAAE;qCAAC;;wBAC5D;mEAAG,aAAa,KAAK;gBACvB;;QACF;mDAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY;YAChB,UAAU;YACV,WAAW,qBAAqB,QAAQ,CAAC,aAAa,EAAE,IACpD,2BACA;YACJ,SAAS,qBAAqB,QAAQ,CAAC,aAAa,EAAE,IAAI,IAAI;YAC9D,YAAY;YACZ,GAAG,aAAa,QAAQ;QAC1B;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAoC,OAAO;YAAC,YAAY;QAAiE;kBAC1I,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;gCAAsD,OAAO;oCAAC,YAAY;gCAAmE;;;;;;0CAG5J,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAA6E,OAAO;wCAAC,YAAY;oCAAmE;8CAEjL,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;0DAK3C,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAS1B,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;wBAEC,OAAO,qBAAqB;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAiF,OAAO;4CAAC,YAAY;wCAAmE;kDACrL,cAAA,6LAAC;4CAAK,WAAU;sDAAW,aAAa,IAAI;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,OAAO;oDAAC,UAAU;oDAAkB,OAAO;gDAAoB;gDAAG,WAAU;0DAC7E,aAAa,KAAK;;;;;;0DAErB,6LAAC;gDAAE,OAAO;oDAAC,UAAU;oDAAkB,OAAO;gDAAoB;gDAAG,WAAU;0DAC5E,aAAa,OAAO;;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;uBAxBrC,aAAa,EAAE;;;;;8BA8BxB,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GAnGwB;KAAA", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/ZigzagAnimatedSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst slides = [\r\n  {\r\n    title: \"Enterprise-Ready Security\",\r\n    desc: \"Your audio and transcripts are protected with industry-leading encryption and privacy controls. Trusted by top organizations worldwide.\",\r\n    cta: \"Learn More\",\r\n    logos: [\r\n      { name: \"<PERSON>\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"#23272a\"/><ellipse cx=\"20\" cy=\"20\" rx=\"12\" ry=\"8\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Epicurious\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"5\" y=\"15\" width=\"30\" height=\"10\" rx=\"5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"25\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#23272a\"/></svg> },\r\n      { name: \"GlobalBank\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><polygon points=\"20,5 35,35 5,35\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"25\" r=\"5\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><circle cx=\"160\" cy=\"160\" r=\"120\" fill=\"#23272a\"/><rect x=\"100\" y=\"140\" width=\"120\" height=\"40\" rx=\"20\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"160\" width=\"80\" height=\"20\" rx=\"10\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Loved by Creators\",\r\n    desc: \"Content creators, podcasters, and educators rely on our AI to turn their voice into polished, shareable text. Join a global community of storytellers.\",\r\n    cta: \"See User Stories\",\r\n    logos: [\r\n      { name: \"Catalog\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"20\" r=\"10\" fill=\"#23272a\"/></svg> },\r\n      { name: \"Luminous\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"10\" y=\"10\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"20\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"30\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Quotient\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><rect x=\"18\" y=\"10\" width=\"4\" height=\"20\" rx=\"2\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"60\" y=\"60\" width=\"200\" height=\"200\" rx=\"40\" fill=\"#6366f1\" opacity=\"0.7\" /><circle cx=\"160\" cy=\"160\" r=\"60\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Seamless Integrations\",\r\n    desc: \"Connect with your favorite tools—export transcripts to Google Docs, Slack, Notion, and more. Workflows that fit your needs.\",\r\n    cta: \"Explore Integrations\",\r\n    logos: [\r\n      { name: \"Slack\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><rect x='10' y='18' width='20' height='4' rx='2' fill='#23272a'/></svg> },\r\n      { name: \"Notion\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Google Docs\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"100\" y=\"100\" width=\"120\" height=\"120\" rx=\"30\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"120\" width=\"80\" height=\"80\" rx=\"20\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Global Language Support\",\r\n    desc: \"Transcribe and translate in 30+ languages. Our AI breaks down barriers and connects you to the world.\",\r\n    cta: \"See Supported Languages\",\r\n    logos: [\r\n      { name: \"World\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='18' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='10' ry='6' fill='#23272a'/></svg> },\r\n      { name: \"Translate\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Globe\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='12' ry='8' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><ellipse cx=\"160\" cy=\"160\" rx=\"120\" ry=\"80\" fill=\"#6366f1\" opacity=\"0.7\" /><ellipse cx=\"160\" cy=\"160\" rx=\"60\" ry=\"40\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function ZigzagAnimatedSection() {\r\n  const rowRefs = useRef([]);\r\n\r\n  useEffect(() => {\r\n    rowRefs.current.forEach((el, i) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 100, x: i % 2 === 0 ? -100 : 100, scale: 0.95 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          x: 0,\r\n          scale: 1,\r\n          duration: 1.1,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 85%\",\r\n            toggleActions: \"play none none none\",\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  // Use the same radial gradient as the Hero/global background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full\" style={sectionBg}>\r\n      {slides.map((slide, i) => (\r\n        <div\r\n          key={i}\r\n          ref={el => (rowRefs.current[i] = el)}\r\n          className={`relative flex flex-col md:flex-row items-center justify-center min-h-screen py-12 md:py-0 px-4 md:px-16 gap-10 md:gap-0`}\r\n        >\r\n          {/* Context */}\r\n          <div className={`flex-1 flex flex-col justify-center items-${i % 2 === 0 ? 'start' : 'end'} z-10`}>\r\n            <div className=\"bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full backdrop-blur-md\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-100 mb-4 leading-tight\">{slide.title}</h2>\r\n              <p className=\"text-gray-300 text-lg mb-6\">{slide.desc}</p>\r\n              <div className=\"flex flex-wrap gap-4 mb-6\">\r\n                {slide.logos.map((logo, idx) => (\r\n                  <div key={logo.name + idx} className=\"flex items-center gap-2 bg-[#23272a]/80 rounded-lg px-3 py-2 shadow\">\r\n                    {logo.svg}\r\n                    <span className=\"text-gray-200 font-semibold text-base\">{logo.name}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <button className=\"mt-2 px-6 py-2 rounded-full bg-indigo-600 text-white font-semibold shadow hover:bg-indigo-500 transition\">{slide.cta}</button>\r\n            </div>\r\n          </div>\r\n          {/* Animation */}\r\n          <div className=\"flex-1 flex items-center justify-center z-10\">\r\n            <div className=\"rounded-2xl shadow-2xl bg-[#181d20]/80 p-6 md:p-12 flex items-center justify-center\">\r\n              {slide.animation}\r\n            </div>\r\n          </div>\r\n          {/* Decorative overlay for depth */}\r\n          <div className=\"absolute inset-0 pointer-events-none z-0\">\r\n            <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1440 900\" fill=\"none\" className=\"w-full h-full\">\r\n              <defs>\r\n                <radialGradient id={`glow${i}`} cx=\"50%\" cy=\"50%\" r=\"70%\">\r\n                  <stop offset=\"0%\" stopColor=\"#6366f1\" stopOpacity=\"0.08\" />\r\n                  <stop offset=\"100%\" stopColor=\"transparent\" />\r\n                </radialGradient>\r\n              </defs>\r\n              <rect width=\"1440\" height=\"900\" fill={`url(#glow${i})`} />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,SAAS;IACb;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAClM;gBAAE,MAAM;gBAAc,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YAClO;gBAAE,MAAM;gBAAc,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAQ,QAAO;4BAAkB,MAAK;;;;;;sCAAW,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC/L;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAM,MAAK;;;;;;8BAAW,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEvR;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;;;;;;;YAAkB;YACzL;gBAAE,MAAM;gBAAY,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YACrS;gBAAE,MAAM;gBAAY,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAI,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC7M;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEhN;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YACzM;gBAAE,MAAM;gBAAU,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC5N;gBAAE,MAAM;gBAAe,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAClO;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEtO;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAChM;gBAAE,MAAM;gBAAa,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC/N;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SACjM;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEjN;CACD;AAEc,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,QAAQ,OAAO,CAAC,OAAO;mDAAC,CAAC,IAAI;oBAC3B,IAAI,CAAC,IAAI;oBACT,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;wBAAE,SAAS;wBAAG,GAAG;wBAAK,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM;wBAAK,OAAO;oBAAK,GAC/D;wBACE,SAAS;wBACT,GAAG;wBACH,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBAEJ;;QACF;0CAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAS,OAAO;kBAChC,OAAO,GAAG,CAAC,CAAC,OAAO,kBAClB,6LAAC;gBAEC,KAAK,CAAA,KAAO,QAAQ,OAAO,CAAC,EAAE,GAAG;gBACjC,WAAY;;kCAGZ,6LAAC;wBAAI,WAAW,AAAC,6CAA0E,OAA9B,IAAI,MAAM,IAAI,UAAU,OAAM;kCACzF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmE,MAAM,KAAK;;;;;;8CAC5F,6LAAC;oCAAE,WAAU;8CAA8B,MAAM,IAAI;;;;;;8CACrD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACtB,6LAAC;4CAA0B,WAAU;;gDAClC,KAAK,GAAG;8DACT,6LAAC;oDAAK,WAAU;8DAAyC,KAAK,IAAI;;;;;;;2CAF1D,KAAK,IAAI,GAAG;;;;;;;;;;8CAM1B,6LAAC;oCAAO,WAAU;8CAA4G,MAAM,GAAG;;;;;;;;;;;;;;;;;kCAI3I,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,SAAS;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAAO,QAAO;4BAAO,SAAQ;4BAAe,MAAK;4BAAO,WAAU;;8CAC3E,6LAAC;8CACC,cAAA,6LAAC;wCAAe,IAAI,AAAC,OAAQ,OAAF;wCAAK,IAAG;wCAAM,IAAG;wCAAM,GAAE;;0DAClD,6LAAC;gDAAK,QAAO;gDAAK,WAAU;gDAAU,aAAY;;;;;;0DAClD,6LAAC;gDAAK,QAAO;gDAAO,WAAU;;;;;;;;;;;;;;;;;8CAGlC,6LAAC;oCAAK,OAAM;oCAAO,QAAO;oCAAM,MAAM,AAAC,YAAa,OAAF,GAAE;;;;;;;;;;;;;;;;;;eAnCnD;;;;;;;;;;AA0Cf;GA7EwB;KAAA", "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyImageScrollSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useRef, useEffect, useState } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst contentBlocks = [\r\n  {\r\n    title: \"Lightning-Fast Transcription\",\r\n    desc: \"Experience real-time speech-to-text conversion with industry-leading accuracy and speed. Perfect for meetings, lectures, and interviews.\",\r\n    image: \"https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Seamless Collaboration\",\r\n    desc: \"Share transcripts instantly with your team, add comments, and keep everyone in sync. Collaboration has never been easier.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Multi-Language Support\",\r\n    desc: \"Break language barriers with support for 30+ languages and dialects. Our AI adapts to your needs, wherever you are.\",\r\n    image: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Enterprise-Grade Security\",\r\n    desc: \"Your data is protected with end-to-end encryption and strict privacy controls. Trusted by organizations worldwide.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n];\r\n\r\nexport default function StickyImageScrollSection() {\r\n  const [activeIdx, setActiveIdx] = useState(0);\r\n  const contentRefs = useRef([]);\r\n  const imageRef = useRef();\r\n  const imageAnimRef = useRef();\r\n\r\n  useEffect(() => {\r\n    contentRefs.current.forEach((el, idx) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 60 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          duration: 0.8,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 70%\",\r\n            toggleActions: \"play none none none\",\r\n            onEnter: () => setActiveIdx(idx),\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!imageAnimRef.current) return;\r\n    gsap.fromTo(\r\n      imageAnimRef.current,\r\n      { opacity: 0, scale: 0.96, y: 40 },\r\n      { opacity: 1, scale: 1, y: 0, duration: 0.7, ease: \"power3.out\" }\r\n    );\r\n  }, [activeIdx]);\r\n\r\n  // Unified background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full py-24 px-4\" style={sectionBg}>\r\n      <div className=\"max-w-7xl mx-auto flex flex-col md:flex-row gap-12 md:gap-0 min-h-[80vh]\">\r\n        {/* Left: Content blocks */}\r\n        <div className=\"flex-1 flex flex-col gap-16 justify-center\">\r\n          {contentBlocks.map((block, idx) => (\r\n            <div\r\n              key={idx}\r\n              ref={el => (contentRefs.current[idx] = el)}\r\n              className={`bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full mb-4 transition-all duration-500 ${activeIdx === idx ? 'ring-2 ring-indigo-500/40' : ''}`}\r\n              style={{ minHeight: 180 }}\r\n            >\r\n              <h3 className=\"text-3xl md:text-4xl font-bold text-gray-100 mb-3\">{block.title}</h3>\r\n              <p className=\"text-gray-300 text-lg\">{block.desc}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        {/* Right: Sticky image */}\r\n        <div className=\"flex-1 flex items-center justify-center relative\">\r\n          <div\r\n            className=\"sticky top-32 w-full max-w-md h-[420px] flex items-center justify-center rounded-2xl overflow-hidden shadow-2xl bg-[#181d20]/80\"\r\n            ref={imageRef}\r\n            style={{ minHeight: 320 }}\r\n          >\r\n            <img\r\n              key={activeIdx}\r\n              ref={imageAnimRef}\r\n              src={contentBlocks[activeIdx].image}\r\n              alt={contentBlocks[activeIdx].title}\r\n              className=\"object-cover w-full h-full transition-all duration-700\"\r\n              style={{ borderRadius: 18 }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,YAAY,OAAO,CAAC,OAAO;sDAAC,CAAC,IAAI;oBAC/B,IAAI,CAAC,IAAI;oBACT,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;4BACf,OAAO;sEAAE,IAAM,aAAa;;wBAC9B;oBACF;gBAEJ;;QACF;6CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,IAAI,CAAC,aAAa,OAAO,EAAE;YAC3B,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,aAAa,OAAO,EACpB;gBAAE,SAAS;gBAAG,OAAO;gBAAM,GAAG;YAAG,GACjC;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa;QAEpE;6CAAG;QAAC;KAAU;IAEd,qBAAqB;IACrB,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAoB,OAAO;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,oBACzB,6LAAC;4BAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;4BACvC,WAAW,AAAC,+FAAmJ,OAArD,cAAc,MAAM,8BAA8B;4BAC5J,OAAO;gCAAE,WAAW;4BAAI;;8CAExB,6LAAC;oCAAG,WAAU;8CAAqD,MAAM,KAAK;;;;;;8CAC9E,6LAAC;oCAAE,WAAU;8CAAyB,MAAM,IAAI;;;;;;;2BAN3C;;;;;;;;;;8BAWX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,KAAK;wBACL,OAAO;4BAAE,WAAW;wBAAI;kCAExB,cAAA,6LAAC;4BAEC,KAAK;4BACL,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,WAAU;4BACV,OAAO;gCAAE,cAAc;4BAAG;2BALrB;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnB;GA/EwB;KAAA", "debugId": null}}]}