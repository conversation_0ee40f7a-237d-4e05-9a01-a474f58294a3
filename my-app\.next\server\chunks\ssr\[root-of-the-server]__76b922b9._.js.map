{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Navbar.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\n\r\nexport default function Navbar() {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [lastScrollY, setLastScrollY] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const controlNavbar = () => {\r\n      if (typeof window !== 'undefined') {\r\n        if (window.scrollY > lastScrollY && window.scrollY > 100) {\r\n          // Scrolling down & past 100px\r\n          setIsVisible(false);\r\n        } else {\r\n          // Scrolling up\r\n          setIsVisible(true);\r\n        }\r\n        setLastScrollY(window.scrollY);\r\n      }\r\n    };\r\n\r\n    if (typeof window !== 'undefined') {\r\n      window.addEventListener('scroll', controlNavbar);\r\n      return () => {\r\n        window.removeEventListener('scroll', controlNavbar);\r\n      };\r\n    }\r\n  }, [lastScrollY]);\r\n\r\n  return (\r\n    <nav className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ${\r\n      isVisible ? 'translate-y-0' : '-translate-y-full'\r\n    }`} style={{backgroundColor: 'var(--neutral-900)', borderBottom: '1px solid var(--neutral-800)'}}>\r\n      <div className=\"max-w-7xl mx-auto flex items-center justify-between py-4 px-6\">\r\n        {/* Logo */}\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {/* Kambaa Logo */}\r\n            <div className=\"relative\">\r\n              <div className=\"w-8 h-8 rounded-full\" style={{backgroundColor: '#7DD3FC'}}></div>\r\n              <div className=\"absolute top-1 left-2 w-6 h-6 rounded-full\" style={{backgroundColor: '#A78BFA'}}></div>\r\n            </div>\r\n            <span className=\"font-bold text-xl\" style={{color: 'var(--primary-600)'}}>Kambaa</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation Links - Centered */}\r\n        <div className=\"hidden md:flex items-center gap-8 absolute left-1/2 transform -translate-x-1/2\">\r\n          <a href=\"#\" className=\"font-medium text-sm transition-colors duration-200 hover:text-primary-400\" style={{color: 'var(--neutral-300)'}}>\r\n            Home\r\n          </a>\r\n          <a href=\"#\" className=\"font-medium text-sm transition-colors duration-200 hover:text-primary-400\" style={{color: 'var(--neutral-300)'}}>\r\n            About\r\n          </a>\r\n          <a href=\"#\" className=\"font-medium text-sm transition-colors duration-200 hover:text-primary-400\" style={{color: 'var(--neutral-300)'}}>\r\n            Pricing\r\n          </a>\r\n        </div>\r\n\r\n        {/* Sign In Button */}\r\n        <div className=\"flex items-center\">\r\n          <button className=\"px-6 py-2 rounded-lg font-medium text-sm transition-all duration-200 hover:bg-primary-700\" style={{backgroundColor: 'var(--primary-600)', color: 'white'}}>\r\n            Sign In\r\n          </button>\r\n        </div>\r\n\r\n        {/* Mobile menu button */}\r\n        <button className=\"md:hidden\" style={{color: 'var(--neutral-300)'}}>\r\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n          </svg>\r\n        </button>\r\n      </div>\r\n    </nav>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB;;QAUF;QAEA;;IAMF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAW,CAAC,kEAAkE,EACjF,YAAY,kBAAkB,qBAC9B;QAAE,OAAO;YAAC,iBAAiB;YAAsB,cAAc;QAA8B;kBAC7F,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAuB,OAAO;4CAAC,iBAAiB;wCAAS;;;;;;kDACxE,8OAAC;wCAAI,WAAU;wCAA6C,OAAO;4CAAC,iBAAiB;wCAAS;;;;;;;;;;;;0CAEhG,8OAAC;gCAAK,WAAU;gCAAoB,OAAO;oCAAC,OAAO;gCAAoB;0CAAG;;;;;;;;;;;;;;;;;8BAK9E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,MAAK;4BAAI,WAAU;4BAA4E,OAAO;gCAAC,OAAO;4BAAoB;sCAAG;;;;;;sCAGxI,8OAAC;4BAAE,MAAK;4BAAI,WAAU;4BAA4E,OAAO;gCAAC,OAAO;4BAAoB;sCAAG;;;;;;sCAGxI,8OAAC;4BAAE,MAAK;4BAAI,WAAU;4BAA4E,OAAO;gCAAC,OAAO;4BAAoB;sCAAG;;;;;;;;;;;;8BAM1I,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAO,WAAU;wBAA4F,OAAO;4BAAC,iBAAiB;4BAAsB,OAAO;wBAAO;kCAAG;;;;;;;;;;;8BAMhL,8OAAC;oBAAO,WAAU;oBAAY,OAAO;wBAAC,OAAO;oBAAoB;8BAC/D,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyAnalyticsSection.js"], "sourcesContent": ["\"use client\"\nimport React, { useRef, useEffect, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\ngsap.registerPlugin(ScrollTrigger);\n\nconst analyticsFeatures = [\n  {\n    id: 1,\n    title: \"Real-Time Speech Recognition\",\n    description: \"Experience instant voice-to-text conversion with our advanced AI. Perfect for live meetings, interviews, and lectures with zero delay.\",\n    icon: \"🎤\",\n    bgColor: \"bg-blue-50\",\n    iconBg: \"bg-blue-100\"\n  },\n  {\n    id: 2,\n    title: \"Multi-Language Support\",\n    description: \"Break language barriers with support for 50+ languages and dialects. Our AI adapts to accents and speaking styles automatically.\",\n    icon: \"🌍\",\n    bgColor: \"bg-green-50\",\n    iconBg: \"bg-green-100\"\n  },\n  {\n    id: 3,\n    title: \"Smart Audio Enhancement\",\n    description: \"Advanced noise reduction and audio clarity enhancement ensure perfect transcription even in challenging environments.\",\n    icon: \"🔊\",\n    bgColor: \"bg-purple-50\",\n    iconBg: \"bg-purple-100\"\n  },\n  {\n    id: 4,\n    title: \"Export & Integration\",\n    description: \"Export transcripts in multiple formats (TXT, DOCX, SRT, VTT) and integrate seamlessly with your favorite productivity tools.\",\n    icon: \"📄\",\n    bgColor: \"bg-orange-50\",\n    iconBg: \"bg-orange-100\"\n  }\n];\n\nexport default function StickyAnalyticsSection() {\n  const [activeIdx, setActiveIdx] = useState(0);\n  const contentRefs = useRef([]);\n  const sectionRef = useRef();\n\n  useEffect(() => {\n    contentRefs.current.forEach((el, idx) => {\n      if (!el) return;\n      \n      ScrollTrigger.create({\n        trigger: el,\n        start: \"top center\",\n        end: \"bottom center\",\n        onEnter: () => setActiveIdx(idx),\n        onEnterBack: () => setActiveIdx(idx),\n      });\n    });\n\n    return () => {\n      ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n    };\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"w-full py-24 px-4\" style={{backgroundColor: 'var(--neutral-50)'}}>\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Left side - Sticky content */}\n          <div className=\"lg:sticky lg:top-32 lg:h-fit\">\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight\" style={{color: 'var(--neutral-900)'}}>\n              We make audio transcription effortless\n            </h2>\n            <p className=\"text-lg md:text-xl mb-8 leading-relaxed\" style={{color: 'var(--neutral-600)'}}>\n              Transform your audio into accurate text with Kambaa's AI-powered transcription service. Simple, fast, and reliable - perfect for professionals, students, and content creators.\n            </p>\n            <button className=\"btn-primary text-lg px-8 py-4\">\n              Start Transcribing\n            </button>\n          </div>\n          \n          {/* Right side - Scrollable feature boxes */}\n          <div className=\"space-y-8\">\n            {analyticsFeatures.map((feature, idx) => (\n              <div\n                key={feature.id}\n                ref={el => (contentRefs.current[idx] = el)}\n                className={`${feature.bgColor} rounded-2xl p-8 border-2 transition-all duration-500 ${\n                  activeIdx === idx ? 'border-gray-300 shadow-lg scale-105' : 'border-transparent'\n                }`}\n                style={{ minHeight: '300px' }}\n              >\n                <div className=\"flex items-start gap-6\">\n                  <div className={`${feature.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center text-2xl flex-shrink-0`}>\n                    {feature.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 style={{fontSize: 'var(--text-2xl)', color: 'var(--neutral-900)'}} className=\"font-bold mb-4\">\n                      {feature.title}\n                    </h3>\n                    <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-700)'}} className=\"leading-relaxed\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n                \n                {/* Visual representation based on feature */}\n                <div className=\"mt-8\">\n                  {idx === 0 && (\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"relative\">\n                        <div className=\"w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-lg\">\n                          <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center\">\n                            <span className=\"text-white text-2xl\">🎤</span>\n                          </div>\n                        </div>\n                        <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full animate-pulse\"></div>\n                        <div className=\"absolute -bottom-2 -left-2 w-6 h-6 bg-blue-300 rounded-full animate-pulse delay-100\"></div>\n                        <div className=\"absolute top-8 -left-8 w-4 h-4 bg-blue-200 rounded-full animate-pulse delay-200\"></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 1 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"flex items-center justify-center gap-2 mb-2\">\n                        <span className=\"text-sm text-gray-600\">🇺🇸 🇪🇸 🇫🇷 🇩🇪 🇯🇵</span>\n                      </div>\n                      <div className=\"flex items-end justify-between h-16\">\n                        <div className=\"w-6 bg-green-300 rounded-t\" style={{height: '40%'}}></div>\n                        <div className=\"w-6 bg-green-400 rounded-t\" style={{height: '60%'}}></div>\n                        <div className=\"w-6 bg-green-500 rounded-t\" style={{height: '80%'}}></div>\n                        <div className=\"w-6 bg-green-600 rounded-t\" style={{height: '100%'}}></div>\n                        <div className=\"w-6 bg-green-400 rounded-t\" style={{height: '70%'}}></div>\n                        <div className=\"w-6 bg-green-500 rounded-t\" style={{height: '85%'}}></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 2 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"flex items-center justify-center mb-4\">\n                        <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center\">\n                          <span className=\"text-2xl\">🔊</span>\n                        </div>\n                      </div>\n                      <div className=\"flex items-end justify-center gap-1 h-12\">\n                        <div className=\"w-2 bg-purple-300 rounded-t\" style={{height: '30%'}}></div>\n                        <div className=\"w-2 bg-purple-500 rounded-t\" style={{height: '70%'}}></div>\n                        <div className=\"w-2 bg-purple-600 rounded-t\" style={{height: '100%'}}></div>\n                        <div className=\"w-2 bg-purple-400 rounded-t\" style={{height: '50%'}}></div>\n                        <div className=\"w-2 bg-purple-500 rounded-t\" style={{height: '80%'}}></div>\n                      </div>\n                    </div>\n                  )}\n\n                  {idx === 3 && (\n                    <div className=\"flex justify-center gap-3\">\n                      <div className=\"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">TXT</div>\n                      <div className=\"w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">DOC</div>\n                      <div className=\"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">SRT</div>\n                      <div className=\"w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs font-bold\">VTT</div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAIA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAEjC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI;YAC/B,IAAI,CAAC,IAAI;YAET,qIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBACnB,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,SAAS,IAAM,aAAa;gBAC5B,aAAa,IAAM,aAAa;YAClC;QACF;QAEA,OAAO;YACL,qIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAA,UAAW,QAAQ,IAAI;QACxD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;QAAoB,OAAO;YAAC,iBAAiB;QAAmB;kBAClG,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;gCAAgE,OAAO;oCAAC,OAAO;gCAAoB;0CAAG;;;;;;0CAGpH,8OAAC;gCAAE,WAAU;gCAA0C,OAAO;oCAAC,OAAO;gCAAoB;0CAAG;;;;;;0CAG7F,8OAAC;gCAAO,WAAU;0CAAgC;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,oBAC/B,8OAAC;gCAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;gCACvC,WAAW,GAAG,QAAQ,OAAO,CAAC,sDAAsD,EAClF,cAAc,MAAM,wCAAwC,sBAC5D;gCACF,OAAO;oCAAE,WAAW;gCAAQ;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,GAAG,QAAQ,MAAM,CAAC,8EAA8E,CAAC;0DAC9G,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,OAAO;4DAAC,UAAU;4DAAmB,OAAO;wDAAoB;wDAAG,WAAU;kEAC9E,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,OAAO;4DAAC,UAAU;4DAAkB,OAAO;wDAAoB;wDAAG,WAAU;kEAC5E,QAAQ,WAAW;;;;;;;;;;;;;;;;;;kDAM1B,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,mBACP,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;sEAG1C,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;4CAKpB,QAAQ,mBACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAM;;;;;;0EAClE,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EACjE,8OAAC;gEAAI,WAAU;gEAA6B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;;;;;;;;;;;;;4CAKtE,QAAQ,mBACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;kEAG/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,8OAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,8OAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAM;;;;;;0EACnE,8OAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;0EAClE,8OAAC;gEAAI,WAAU;gEAA8B,OAAO;oEAAC,QAAQ;gEAAK;;;;;;;;;;;;;;;;;;4CAKvE,QAAQ,mBACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAmG;;;;;;kEAClH,8OAAC;wDAAI,WAAU;kEAAiG;;;;;;kEAChH,8OAAC;wDAAI,WAAU;kEAAkG;;;;;;kEACjH,8OAAC;wDAAI,WAAU;kEAAmG;;;;;;;;;;;;;;;;;;;+BA5EnH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAuF/B", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/KeyBenefitsSection.js"], "sourcesContent": ["\"use client\"\nimport React from 'react';\n\nconst benefits = [\n  {\n    id: 1,\n    title: \"AI-Powered Accuracy\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <path d=\"M9 12l2 2 4-4\"/>\n        <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n        <path d=\"M8 12l2 2 4-4\"/>\n      </svg>\n    )\n  },\n  {\n    id: 2,\n    title: \"Lightning Fast\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <polygon points=\"13 2 3 14 12 14 11 22 21 10 12 10 13 2\"/>\n      </svg>\n    )\n  },\n  {\n    id: 3,\n    title: \"Secure & Private\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"/>\n        <circle cx=\"12\" cy=\"16\" r=\"1\"/>\n        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"/>\n      </svg>\n    )\n  },\n  {\n    id: 4,\n    title: \"24/7 Support\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"/>\n        <path d=\"M8 9h8\"/>\n        <path d=\"M8 13h6\"/>\n      </svg>\n    )\n  }\n];\n\nexport default function KeyBenefitsSection() {\n  return (\n    <section className=\"w-full py-20 px-4\" style={{backgroundColor: 'var(--neutral-900)'}}>\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left side - Content */}\n          <div className=\"space-y-8\">\n            <div className=\"flex items-center gap-2\" style={{color: 'var(--primary-400)'}}>\n              <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n              <span className=\"text-sm font-semibold uppercase tracking-wider\">KEY BENEFITS</span>\n            </div>\n\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\" style={{color: 'var(--neutral-100)'}}>\n              Start Enjoying Smarter Transcription{' '}\n              <span style={{color: 'var(--primary-400)'}}>Today</span>\n            </h2>\n\n            <p className=\"text-lg md:text-xl leading-relaxed max-w-lg\" style={{color: 'var(--neutral-300)'}}>\n              Experience the future of audio transcription with Kambaa AI. Our advanced speech recognition technology delivers unmatched accuracy and speed, making it the perfect choice for professionals, students, and content creators who demand the best.\n            </p>\n\n            <div className=\"flex items-center gap-4\">\n              <button className=\"btn-secondary\">\n                Try Free Now\n              </button>\n              <div className=\"w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-colors duration-300 hover:bg-indigo-600\" style={{backgroundColor: 'var(--primary-500)'}}>\n                <svg className=\"w-6 h-6 text-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M8 5v14l11-7z\"/>\n                </svg>\n              </div>\n            </div>\n          </div>\n          \n          {/* Right side - Benefits grid */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n            {benefits.map((benefit) => (\n              <div\n                key={benefit.id}\n                className=\"rounded-2xl p-8 border transition-all duration-300 group hover:transform hover:scale-105 hover:border-indigo-400\"\n                style={{\n                  backgroundColor: 'var(--neutral-800)',\n                  opacity: 0.8,\n                  borderColor: 'var(--neutral-700)'\n                }}\n              >\n                <div className=\"mb-6 group-hover:scale-110 transition-transform duration-300\" style={{color: 'var(--primary-400)'}}>\n                  {benefit.icon}\n                </div>\n                <h3 className=\"text-xl font-semibold\" style={{color: 'var(--neutral-100)'}}>\n                  {benefit.title}\n                </h3>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;;;;;;8BAC1B,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;sBAC3F,cAAA,8OAAC;gBAAQ,QAAO;;;;;;;;;;;IAGtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAI,IAAG;;;;;;8BACpD,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;;;;;;8BAC1B,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAoB,OAAO;YAAC,iBAAiB;QAAoB;kBAClF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0B,OAAO;oCAAC,OAAO;gCAAoB;;kDAC1E,8OAAC;wCAAI,WAAU;wCAAU,SAAQ;wCAAY,MAAK;kDAChD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC;gCAAG,WAAU;gCAA2D,OAAO;oCAAC,OAAO;gCAAoB;;oCAAG;oCACxE;kDACrC,8OAAC;wCAAK,OAAO;4CAAC,OAAO;wCAAoB;kDAAG;;;;;;;;;;;;0CAG9C,8OAAC;gCAAE,WAAU;gCAA8C,OAAO;oCAAC,OAAO;gCAAoB;0CAAG;;;;;;0CAIjG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAgB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;wCAA4H,OAAO;4CAAC,iBAAiB;wCAAoB;kDACtL,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,SAAQ;4CAAY,MAAK;sDAC3D,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,SAAS;oCACT,aAAa;gCACf;;kDAEA,8OAAC;wCAAI,WAAU;wCAA+D,OAAO;4CAAC,OAAO;wCAAoB;kDAC9G,QAAQ,IAAI;;;;;;kDAEf,8OAAC;wCAAG,WAAU;wCAAwB,OAAO;4CAAC,OAAO;wCAAoB;kDACtE,QAAQ,KAAK;;;;;;;+BAZX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAqB/B", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/InteractiveNotificationSection.js"], "sourcesContent": ["\"use client\"\nimport React, { useState, useEffect } from 'react';\n\nconst notifications = [\n  {\n    id: 1,\n    type: 'success',\n    icon: '🎤',\n    title: 'Audio transcription completed',\n    message: 'Your 45-minute meeting has been transcribed with 99.8% accuracy',\n    position: { top: '15%', left: '10%' },\n    delay: 0\n  },\n  {\n    id: 2,\n    type: 'info',\n    icon: '📊',\n    title: 'New transcription request',\n    message: 'Processing audio file: \"Team_Meeting_Jan_2024.mp3\"',\n    position: { top: '20%', right: '10%' },\n    delay: 1000\n  },\n  {\n    id: 3,\n    type: 'success',\n    icon: '✨',\n    title: 'AI Enhancement Complete',\n    message: 'Speaker identification and punctuation added automatically',\n    position: { bottom: '10%', left: '20%' },\n    delay: 2000\n  },\n  {\n    id: 4,\n    type: 'notification',\n    icon: '📝',\n    title: 'Export Ready',\n    message: 'Your transcript is ready for download in multiple formats',\n    position: { bottom: '15%', right: '15%' },\n    delay: 3000\n  }\n];\n\nexport default function InteractiveNotificationSection() {\n  const [visibleNotifications, setVisibleNotifications] = useState([]);\n\n  useEffect(() => {\n    notifications.forEach((notification) => {\n      setTimeout(() => {\n        setVisibleNotifications(prev => [...prev, notification.id]);\n      }, notification.delay);\n    });\n  }, []);\n\n  const getNotificationStyle = (notification) => {\n    const baseStyle = {\n      position: 'absolute',\n      transform: visibleNotifications.includes(notification.id) \n        ? 'translateY(0) scale(1)' \n        : 'translateY(20px) scale(0.9)',\n      opacity: visibleNotifications.includes(notification.id) ? 1 : 0,\n      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',\n      ...notification.position\n    };\n    return baseStyle;\n  };\n\n  return (\n    <section className=\"w-full py-20 px-4 overflow-hidden\" style={{background: 'linear-gradient(135deg, var(--primary-50), var(--secondary-50))'}}>\n      <div className=\"max-w-7xl mx-auto relative\">\n        <div className=\"flex items-center justify-center min-h-[600px]\">\n          {/* Central image container */}\n          <div className=\"relative\">\n            {/* Main background shape */}\n            <div className=\"w-96 h-96 rounded-3xl transform rotate-6 shadow-2xl\" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}></div>\n\n            {/* Person with laptop image */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div className=\"w-80 h-80 rounded-2xl flex items-center justify-center transform -rotate-6\" style={{background: 'linear-gradient(135deg, var(--primary-400), var(--secondary-400))'}}>\n                {/* Person illustration */}\n                <div className=\"relative\">\n                  {/* Laptop */}\n                  <div className=\"w-32 h-20 bg-gray-800 rounded-lg mb-4 relative\">\n                    <div className=\"w-30 h-18 bg-gray-100 rounded-sm m-1 flex items-center justify-center\">\n                      <div className=\"text-xs text-gray-600\">🎵 → 📝</div>\n                    </div>\n                  </div>\n                  \n                  {/* Person silhouette */}\n                  <div className=\"w-16 h-16 bg-gray-700 rounded-full mx-auto mb-2\"></div>\n                  <div className=\"w-20 h-12 bg-gray-600 rounded-t-full mx-auto\"></div>\n                  \n                  {/* Headphones */}\n                  <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2\">\n                    <div className=\"w-20 h-4 border-4 border-gray-800 rounded-full\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Floating notification cards */}\n        {notifications.map((notification) => (\n          <div\n            key={notification.id}\n            style={getNotificationStyle(notification)}\n            className=\"bg-white rounded-xl shadow-lg p-4 max-w-xs border border-gray-100 hover:shadow-xl transition-shadow duration-300\"\n          >\n            <div className=\"flex items-start gap-3\">\n              <div className=\"w-10 h-10 rounded-lg flex items-center justify-center text-white flex-shrink-0\" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}>\n                <span className=\"text-lg\">{notification.icon}</span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"text-sm font-semibold mb-1 truncate\" style={{color: 'var(--neutral-900)'}}>\n                  {notification.title}\n                </h4>\n                <p className=\"text-xs leading-relaxed\" style={{color: 'var(--neutral-600)'}}>\n                  {notification.message}\n                </p>\n              </div>\n            </div>\n            \n            {/* Status indicator */}\n            <div className=\"flex items-center justify-between mt-3\">\n              <div className=\"flex items-center gap-1\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500\">Live</span>\n              </div>\n              <span className=\"text-xs text-gray-400\">Just now</span>\n            </div>\n          </div>\n        ))}\n        \n        {/* Background decorative elements */}\n        <div className=\"absolute top-10 left-10 w-20 h-20 bg-orange-200 rounded-full opacity-50 animate-pulse\"></div>\n        <div className=\"absolute bottom-10 right-10 w-16 h-16 bg-yellow-200 rounded-full opacity-50 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-5 w-12 h-12 bg-orange-300 rounded-full opacity-30 animate-bounce\"></div>\n        <div className=\"absolute top-1/3 right-5 w-8 h-8 bg-yellow-300 rounded-full opacity-40 animate-bounce delay-500\"></div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,KAAK;YAAO,MAAM;QAAM;QACpC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,KAAK;YAAO,OAAO;QAAM;QACrC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,QAAQ;YAAO,MAAM;QAAM;QACvC,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;YAAE,QAAQ;YAAO,OAAO;QAAM;QACxC,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,CAAC,CAAC;YACrB,WAAW;gBACT,wBAAwB,CAAA,OAAQ;2BAAI;wBAAM,aAAa,EAAE;qBAAC;YAC5D,GAAG,aAAa,KAAK;QACvB;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY;YAChB,UAAU;YACV,WAAW,qBAAqB,QAAQ,CAAC,aAAa,EAAE,IACpD,2BACA;YACJ,SAAS,qBAAqB,QAAQ,CAAC,aAAa,EAAE,IAAI,IAAI;YAC9D,YAAY;YACZ,GAAG,aAAa,QAAQ;QAC1B;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAoC,OAAO;YAAC,YAAY;QAAiE;kBAC1I,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;gCAAsD,OAAO;oCAAC,YAAY;gCAAmE;;;;;;0CAG5J,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA6E,OAAO;wCAAC,YAAY;oCAAmE;8CAEjL,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAS1B,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wBAEC,OAAO,qBAAqB;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAiF,OAAO;4CAAC,YAAY;wCAAmE;kDACrL,cAAA,8OAAC;4CAAK,WAAU;sDAAW,aAAa,IAAI;;;;;;;;;;;kDAE9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;gDAAsC,OAAO;oDAAC,OAAO;gDAAoB;0DACpF,aAAa,KAAK;;;;;;0DAErB,8OAAC;gDAAE,WAAU;gDAA0B,OAAO;oDAAC,OAAO;gDAAoB;0DACvE,aAAa,OAAO;;;;;;;;;;;;;;;;;;0CAM3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;uBAxBrC,aAAa,EAAE;;;;;8BA8BxB,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/ZigzagAnimatedSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst slides = [\r\n  {\r\n    title: \"Enterprise-Ready Security\",\r\n    desc: \"Your audio and transcripts are protected with industry-leading encryption and privacy controls. Trusted by top organizations worldwide.\",\r\n    cta: \"Learn More\",\r\n    logos: [\r\n      { name: \"<PERSON>\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"#23272a\"/><ellipse cx=\"20\" cy=\"20\" rx=\"12\" ry=\"8\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Epicurious\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"5\" y=\"15\" width=\"30\" height=\"10\" rx=\"5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"25\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#23272a\"/></svg> },\r\n      { name: \"GlobalBank\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><polygon points=\"20,5 35,35 5,35\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"25\" r=\"5\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><circle cx=\"160\" cy=\"160\" r=\"120\" fill=\"#23272a\"/><rect x=\"100\" y=\"140\" width=\"120\" height=\"40\" rx=\"20\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"160\" width=\"80\" height=\"20\" rx=\"10\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Loved by Creators\",\r\n    desc: \"Content creators, podcasters, and educators rely on our AI to turn their voice into polished, shareable text. Join a global community of storytellers.\",\r\n    cta: \"See User Stories\",\r\n    logos: [\r\n      { name: \"Catalog\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"20\" r=\"10\" fill=\"#23272a\"/></svg> },\r\n      { name: \"Luminous\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"10\" y=\"10\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"20\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"30\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Quotient\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><rect x=\"18\" y=\"10\" width=\"4\" height=\"20\" rx=\"2\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"60\" y=\"60\" width=\"200\" height=\"200\" rx=\"40\" fill=\"#6366f1\" opacity=\"0.7\" /><circle cx=\"160\" cy=\"160\" r=\"60\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Seamless Integrations\",\r\n    desc: \"Connect with your favorite tools—export transcripts to Google Docs, Slack, Notion, and more. Workflows that fit your needs.\",\r\n    cta: \"Explore Integrations\",\r\n    logos: [\r\n      { name: \"Slack\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><rect x='10' y='18' width='20' height='4' rx='2' fill='#23272a'/></svg> },\r\n      { name: \"Notion\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Google Docs\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"100\" y=\"100\" width=\"120\" height=\"120\" rx=\"30\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"120\" width=\"80\" height=\"80\" rx=\"20\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Global Language Support\",\r\n    desc: \"Transcribe and translate in 30+ languages. Our AI breaks down barriers and connects you to the world.\",\r\n    cta: \"See Supported Languages\",\r\n    logos: [\r\n      { name: \"World\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='18' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='10' ry='6' fill='#23272a'/></svg> },\r\n      { name: \"Translate\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Globe\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='12' ry='8' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><ellipse cx=\"160\" cy=\"160\" rx=\"120\" ry=\"80\" fill=\"#6366f1\" opacity=\"0.7\" /><ellipse cx=\"160\" cy=\"160\" rx=\"60\" ry=\"40\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function ZigzagAnimatedSection() {\r\n  const rowRefs = useRef([]);\r\n\r\n  useEffect(() => {\r\n    rowRefs.current.forEach((el, i) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 100, x: i % 2 === 0 ? -100 : 100, scale: 0.95 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          x: 0,\r\n          scale: 1,\r\n          duration: 1.1,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 85%\",\r\n            toggleActions: \"play none none none\",\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  // Use the same radial gradient as the Hero/global background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full\" style={sectionBg}>\r\n      {slides.map((slide, i) => (\r\n        <div\r\n          key={i}\r\n          ref={el => (rowRefs.current[i] = el)}\r\n          className={`relative flex flex-col md:flex-row items-center justify-center min-h-screen py-12 md:py-0 px-4 md:px-16 gap-10 md:gap-0`}\r\n        >\r\n          {/* Context */}\r\n          <div className={`flex-1 flex flex-col justify-center items-${i % 2 === 0 ? 'start' : 'end'} z-10`}>\r\n            <div className=\"bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full backdrop-blur-md\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-100 mb-4 leading-tight\">{slide.title}</h2>\r\n              <p className=\"text-gray-300 text-lg mb-6\">{slide.desc}</p>\r\n              <div className=\"flex flex-wrap gap-4 mb-6\">\r\n                {slide.logos.map((logo, idx) => (\r\n                  <div key={logo.name + idx} className=\"flex items-center gap-2 bg-[#23272a]/80 rounded-lg px-3 py-2 shadow\">\r\n                    {logo.svg}\r\n                    <span className=\"text-gray-200 font-semibold text-base\">{logo.name}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <button className=\"mt-2 px-6 py-2 rounded-full bg-indigo-600 text-white font-semibold shadow hover:bg-indigo-500 transition\">{slide.cta}</button>\r\n            </div>\r\n          </div>\r\n          {/* Animation */}\r\n          <div className=\"flex-1 flex items-center justify-center z-10\">\r\n            <div className=\"rounded-2xl shadow-2xl bg-[#181d20]/80 p-6 md:p-12 flex items-center justify-center\">\r\n              {slide.animation}\r\n            </div>\r\n          </div>\r\n          {/* Decorative overlay for depth */}\r\n          <div className=\"absolute inset-0 pointer-events-none z-0\">\r\n            <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1440 900\" fill=\"none\" className=\"w-full h-full\">\r\n              <defs>\r\n                <radialGradient id={`glow${i}`} cx=\"50%\" cy=\"50%\" r=\"70%\">\r\n                  <stop offset=\"0%\" stopColor=\"#6366f1\" stopOpacity=\"0.08\" />\r\n                  <stop offset=\"100%\" stopColor=\"transparent\" />\r\n                </radialGradient>\r\n              </defs>\r\n              <rect width=\"1440\" height=\"900\" fill={`url(#glow${i})`} />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAIA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAEjC,MAAM,SAAS;IACb;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAClM;gBAAE,MAAM;gBAAc,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YAClO;gBAAE,MAAM;gBAAc,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAQ,QAAO;4BAAkB,MAAK;;;;;;sCAAW,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC/L;QACD,yBACE,8OAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAM,MAAK;;;;;;8BAAW,8OAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,8OAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEvR;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;;;;;;;YAAkB;YACzL;gBAAE,MAAM;gBAAY,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YACrS;gBAAE,MAAM;gBAAY,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAI,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC7M;QACD,yBACE,8OAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEhN;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YACzM;gBAAE,MAAM;gBAAU,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC5N;gBAAE,MAAM;gBAAe,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAClO;QACD,yBACE,8OAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,8OAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,8OAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEtO;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAChM;gBAAE,MAAM;gBAAa,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,8OAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC/N;gBAAE,MAAM;gBAAS,mBAAK,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,8OAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,8OAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SACjM;QACD,yBACE,8OAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,8OAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEjN;CACD;AAEc,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI;YAC3B,IAAI,CAAC,IAAI;YACT,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;gBAAE,SAAS;gBAAG,GAAG;gBAAK,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM;gBAAK,OAAO;YAAK,GAC/D;gBACE,SAAS;gBACT,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,eAAe;gBACjB;YACF;QAEJ;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAS,OAAO;kBAChC,OAAO,GAAG,CAAC,CAAC,OAAO,kBAClB,8OAAC;gBAEC,KAAK,CAAA,KAAO,QAAQ,OAAO,CAAC,EAAE,GAAG;gBACjC,WAAW,CAAC,uHAAuH,CAAC;;kCAGpI,8OAAC;wBAAI,WAAW,CAAC,0CAA0C,EAAE,IAAI,MAAM,IAAI,UAAU,MAAM,KAAK,CAAC;kCAC/F,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE,MAAM,KAAK;;;;;;8CAC5F,8OAAC;oCAAE,WAAU;8CAA8B,MAAM,IAAI;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACtB,8OAAC;4CAA0B,WAAU;;gDAClC,KAAK,GAAG;8DACT,8OAAC;oDAAK,WAAU;8DAAyC,KAAK,IAAI;;;;;;;2CAF1D,KAAK,IAAI,GAAG;;;;;;;;;;8CAM1B,8OAAC;oCAAO,WAAU;8CAA4G,MAAM,GAAG;;;;;;;;;;;;;;;;;kCAI3I,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,SAAS;;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAAO,QAAO;4BAAO,SAAQ;4BAAe,MAAK;4BAAO,WAAU;;8CAC3E,8OAAC;8CACC,cAAA,8OAAC;wCAAe,IAAI,CAAC,IAAI,EAAE,GAAG;wCAAE,IAAG;wCAAM,IAAG;wCAAM,GAAE;;0DAClD,8OAAC;gDAAK,QAAO;gDAAK,WAAU;gDAAU,aAAY;;;;;;0DAClD,8OAAC;gDAAK,QAAO;gDAAO,WAAU;;;;;;;;;;;;;;;;;8CAGlC,8OAAC;oCAAK,OAAM;oCAAO,QAAO;oCAAM,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;;;;;;;;;;;;;;;;;;eAnCrD;;;;;;;;;;AA0Cf", "debugId": null}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyImageScrollSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useRef, useEffect, useState } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst contentBlocks = [\r\n  {\r\n    title: \"Lightning-Fast Transcription\",\r\n    desc: \"Experience real-time speech-to-text conversion with industry-leading accuracy and speed. Perfect for meetings, lectures, and interviews.\",\r\n    image: \"https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Seamless Collaboration\",\r\n    desc: \"Share transcripts instantly with your team, add comments, and keep everyone in sync. Collaboration has never been easier.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Multi-Language Support\",\r\n    desc: \"Break language barriers with support for 30+ languages and dialects. Our AI adapts to your needs, wherever you are.\",\r\n    image: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Enterprise-Grade Security\",\r\n    desc: \"Your data is protected with end-to-end encryption and strict privacy controls. Trusted by organizations worldwide.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n];\r\n\r\nexport default function StickyImageScrollSection() {\r\n  const [activeIdx, setActiveIdx] = useState(0);\r\n  const contentRefs = useRef([]);\r\n  const imageRef = useRef();\r\n  const imageAnimRef = useRef();\r\n\r\n  useEffect(() => {\r\n    contentRefs.current.forEach((el, idx) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 60 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          duration: 0.8,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 70%\",\r\n            toggleActions: \"play none none none\",\r\n            onEnter: () => setActiveIdx(idx),\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!imageAnimRef.current) return;\r\n    gsap.fromTo(\r\n      imageAnimRef.current,\r\n      { opacity: 0, scale: 0.96, y: 40 },\r\n      { opacity: 1, scale: 1, y: 0, duration: 0.7, ease: \"power3.out\" }\r\n    );\r\n  }, [activeIdx]);\r\n\r\n  // Unified background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full py-24 px-4\" style={sectionBg}>\r\n      <div className=\"max-w-7xl mx-auto flex flex-col md:flex-row gap-12 md:gap-0 min-h-[80vh]\">\r\n        {/* Left: Content blocks */}\r\n        <div className=\"flex-1 flex flex-col gap-16 justify-center\">\r\n          {contentBlocks.map((block, idx) => (\r\n            <div\r\n              key={idx}\r\n              ref={el => (contentRefs.current[idx] = el)}\r\n              className={`bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full mb-4 transition-all duration-500 ${activeIdx === idx ? 'ring-2 ring-indigo-500/40' : ''}`}\r\n              style={{ minHeight: 180 }}\r\n            >\r\n              <h3 className=\"text-3xl md:text-4xl font-bold text-gray-100 mb-3\">{block.title}</h3>\r\n              <p className=\"text-gray-300 text-lg\">{block.desc}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        {/* Right: Sticky image */}\r\n        <div className=\"flex-1 flex items-center justify-center relative\">\r\n          <div\r\n            className=\"sticky top-32 w-full max-w-md h-[420px] flex items-center justify-center rounded-2xl overflow-hidden shadow-2xl bg-[#181d20]/80\"\r\n            ref={imageRef}\r\n            style={{ minHeight: 320 }}\r\n          >\r\n            <img\r\n              key={activeIdx}\r\n              ref={imageAnimRef}\r\n              src={contentBlocks[activeIdx].image}\r\n              alt={contentBlocks[activeIdx].title}\r\n              className=\"object-cover w-full h-full transition-all duration-700\"\r\n              style={{ borderRadius: 18 }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAIA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAEjC,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI;YAC/B,IAAI,CAAC,IAAI;YACT,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBACE,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,eAAe;oBACf,SAAS,IAAM,aAAa;gBAC9B;YACF;QAEJ;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,EAAE;QAC3B,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,aAAa,OAAO,EACpB;YAAE,SAAS;YAAG,OAAO;YAAM,GAAG;QAAG,GACjC;YAAE,SAAS;YAAG,OAAO;YAAG,GAAG;YAAG,UAAU;YAAK,MAAM;QAAa;IAEpE,GAAG;QAAC;KAAU;IAEd,qBAAqB;IACrB,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAoB,OAAO;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,oBACzB,8OAAC;4BAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;4BACvC,WAAW,CAAC,4FAA4F,EAAE,cAAc,MAAM,8BAA8B,IAAI;4BAChK,OAAO;gCAAE,WAAW;4BAAI;;8CAExB,8OAAC;oCAAG,WAAU;8CAAqD,MAAM,KAAK;;;;;;8CAC9E,8OAAC;oCAAE,WAAU;8CAAyB,MAAM,IAAI;;;;;;;2BAN3C;;;;;;;;;;8BAWX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAU;wBACV,KAAK;wBACL,OAAO;4BAAE,WAAW;wBAAI;kCAExB,cAAA,8OAAC;4BAEC,KAAK;4BACL,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,WAAU;4BACV,OAAO;gCAAE,cAAc;4BAAG;2BALrB;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnB", "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/TestimonialCarousel.js"], "sourcesContent": ["\"use client\"\nimport React, { useState, useEffect } from 'react';\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON>\",\n    role: \"Content Creator\",\n    company: \"Digital Media Co.\",\n    avatar: \"SJ\",\n    content: \"Kambaa AI has revolutionized my workflow. I can transcribe hours of interviews in minutes with incredible accuracy. It's a game-changer for content creators.\",\n    rating: 5\n  },\n  {\n    id: 2,\n    name: \"<PERSON>\",\n    role: \"Podcast Host\",\n    company: \"Tech Talk Podcast\",\n    avatar: \"MC\",\n    content: \"The accuracy is phenomenal! I've tried many transcription services, but Kambaa AI consistently delivers 99%+ accuracy even with technical jargon.\",\n    rating: 5\n  },\n  {\n    id: 3,\n    name: \"Dr. <PERSON>\",\n    role: \"Medical Researcher\",\n    company: \"Stanford Medical\",\n    avatar: \"ER\",\n    content: \"Perfect for transcribing medical interviews and research sessions. The multi-language support and speaker identification features are outstanding.\",\n    rating: 5\n  },\n  {\n    id: 4,\n    name: \"<PERSON>\",\n    role: \"Business Analyst\",\n    company: \"Fortune 500 Corp\",\n    avatar: \"JW\",\n    content: \"We use Kambaa AI for all our meeting transcriptions. The real-time processing and export options have improved our team's productivity by 300%.\",\n    rating: 5\n  },\n  {\n    id: 5,\n    name: \"<PERSON>\",\n    role: \"Journalist\",\n    company: \"News Network\",\n    avatar: \"L<PERSON>\",\n    content: \"As a journalist, accuracy and speed are crucial. Kambaa AI delivers both flawlessly. The noise reduction feature works perfectly even in busy environments.\",\n    rating: 5\n  },\n  {\n    id: 6,\n    name: \"David Park\",\n    role: \"Student\",\n    company: \"MIT\",\n    avatar: \"DP\",\n    content: \"This tool has been invaluable for transcribing lectures and study sessions. The affordable pricing makes it accessible for students like me.\",\n    rating: 5\n  }\n];\n\nexport default function TestimonialCarousel() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n    \n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => \n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 4000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const goToSlide = (index) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds\n  };\n\n  const nextSlide = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n    );\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  const prevSlide = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1\n    );\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  };\n\n  return (\n    <section className=\"w-full py-24 px-4\"style={{background: 'linear-gradient(135deg, var(--primary-50), var(--secondary-50))'}}>\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 px-4 py-2 rounded-full mb-6 border\" style={{backgroundColor: 'var(--primary-100)', borderColor: 'var(--primary-200)', color: 'var(--primary-700)'}}>\n            <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n            </svg>\n            <span className=\"text-sm font-semibold uppercase tracking-wider\">Testimonials</span>\n          </div>\n          \n          <h2 className=\"font-bold mb-4 leading-tight\" style={{fontSize: 'var(--text-4xl)', color: 'var(--neutral-900)'}}>\n            What Our Users Say About{' '}\n            <span style={{color: 'var(--primary-600)'}}>Kambaa AI</span>\n          </h2>\n          \n          <p className=\"max-w-2xl mx-auto leading-relaxed\" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-600)'}}>\n            Join thousands of satisfied users who have transformed their workflow with our AI-powered transcription service.\n          </p>\n        </div>\n\n        {/* Carousel Container */}\n        <div className=\"relative\">\n          {/* Main Testimonial Display */}\n          <div className=\"bg-white rounded-3xl shadow-xl p-8 md:p-12 relative overflow-hidden\">\n            {/* Background decoration */}\n            <div className=\"absolute top-0 right-0 w-32 h-32 rounded-full blur-2xl opacity-10\" style={{backgroundColor: 'var(--primary-500)'}}></div>\n            \n            {/* Quote icon */}\n            <div className=\"absolute top-8 left-8 opacity-10\">\n              <svg className=\"w-16 h-16\" style={{color: 'var(--primary-500)'}} viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z\" />\n              </svg>\n            </div>\n\n            <div className=\"relative z-10\">\n              {/* Stars */}\n              <div className=\"flex gap-1 mb-6\">\n                {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                  <svg key={i} className=\"w-5 h-5\" style={{color: 'var(--secondary-500)'}} viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                  </svg>\n                ))}\n              </div>\n\n              {/* Testimonial Content */}\n              <blockquote className=\"mb-8 leading-relaxed\" style={{fontSize: 'var(--text-xl)', color: 'var(--neutral-700)'}}>\n                \"{testimonials[currentIndex].content}\"\n              </blockquote>\n\n              {/* Author Info */}\n              <div className=\"flex items-center gap-4\">\n                <div className=\"w-16 h-16 rounded-full flex items-center justify-center font-bold text-white\" style={{backgroundColor: 'var(--primary-600)'}}>\n                  {testimonials[currentIndex].avatar}\n                </div>\n                <div>\n                  <div className=\"font-semibold\" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-900)'}}>\n                    {testimonials[currentIndex].name}\n                  </div>\n                  <div style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-600)'}}>\n                    {testimonials[currentIndex].role} at {testimonials[currentIndex].company}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Arrows */}\n          <button\n            onClick={prevSlide}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110\"\n            style={{backgroundColor: 'white', color: 'var(--primary-600)'}}\n          >\n            <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n              <path d=\"M15 18l-6-6 6-6\"/>\n            </svg>\n          </button>\n\n          <button\n            onClick={nextSlide}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110\"\n            style={{backgroundColor: 'white', color: 'var(--primary-600)'}}\n          >\n            <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n              <path d=\"M9 18l6-6-6-6\"/>\n            </svg>\n          </button>\n        </div>\n\n        {/* Dots Indicator */}\n        <div className=\"flex justify-center gap-3 mt-8\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToSlide(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                index === currentIndex ? 'scale-125' : 'hover:scale-110'\n              }`}\n              style={{\n                backgroundColor: index === currentIndex ? 'var(--primary-600)' : 'var(--neutral-300)'\n              }}\n            />\n          ))}\n        </div>\n\n        {/* Auto-play indicator */}\n        <div className=\"text-center mt-4\">\n          <button\n            onClick={() => setIsAutoPlaying(!isAutoPlaying)}\n            className=\"text-sm px-4 py-2 rounded-full transition-colors duration-300\"\n            style={{color: 'var(--neutral-600)'}}\n          >\n            {isAutoPlaying ? '⏸️ Pause' : '▶️ Play'} Auto-scroll\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;QAE5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAc;IAElB,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO,QAAQ,oCAAoC;IACvF;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;QAE1D,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,YACf,cAAc,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY;QAE1D,iBAAiB;QACjB,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,qBACE,8OAAC;QAAQ,WAAU;QAAmB,OAAO;YAAC,YAAY;QAAiE;kBACzH,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAoE,OAAO;gCAAC,iBAAiB;gCAAsB,aAAa;gCAAsB,OAAO;4BAAoB;;8CAC9L,8OAAC;oCAAI,WAAU;oCAAU,SAAQ;oCAAY,MAAK;8CAChD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;8CAEV,8OAAC;oCAAK,WAAU;8CAAiD;;;;;;;;;;;;sCAGnE,8OAAC;4BAAG,WAAU;4BAA+B,OAAO;gCAAC,UAAU;gCAAmB,OAAO;4BAAoB;;gCAAG;gCACrF;8CACzB,8OAAC;oCAAK,OAAO;wCAAC,OAAO;oCAAoB;8CAAG;;;;;;;;;;;;sCAG9C,8OAAC;4BAAE,WAAU;4BAAoC,OAAO;gCAAC,UAAU;gCAAkB,OAAO;4BAAoB;sCAAG;;;;;;;;;;;;8BAMrH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;oCAAoE,OAAO;wCAAC,iBAAiB;oCAAoB;;;;;;8CAGhI,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAY,OAAO;4CAAC,OAAO;wCAAoB;wCAAG,SAAQ;wCAAY,MAAK;kDACxF,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;6CAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC;oDAAY,WAAU;oDAAU,OAAO;wDAAC,OAAO;oDAAsB;oDAAG,SAAQ;oDAAY,MAAK;8DAChG,cAAA,8OAAC;wDAAK,GAAE;;;;;;mDADA;;;;;;;;;;sDAOd,8OAAC;4CAAW,WAAU;4CAAuB,OAAO;gDAAC,UAAU;gDAAkB,OAAO;4CAAoB;;gDAAG;gDAC3G,YAAY,CAAC,aAAa,CAAC,OAAO;gDAAC;;;;;;;sDAIvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAA+E,OAAO;wDAAC,iBAAiB;oDAAoB;8DACxI,YAAY,CAAC,aAAa,CAAC,MAAM;;;;;;8DAEpC,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;4DAAgB,OAAO;gEAAC,UAAU;gEAAkB,OAAO;4DAAoB;sEAC3F,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;sEAElC,8OAAC;4DAAI,OAAO;gEAAC,UAAU;gEAAkB,OAAO;4DAAoB;;gEACjE,YAAY,CAAC,aAAa,CAAC,IAAI;gEAAC;gEAAK,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlF,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCAAC,iBAAiB;gCAAS,OAAO;4BAAoB;sCAE7D,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;gCAAO,QAAO;gCAAe,aAAY;0CACzF,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;sCAIZ,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAO;gCAAC,iBAAiB;gCAAS,OAAO;4BAAoB;sCAE7D,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;gCAAO,QAAO;gCAAe,aAAY;0CACzF,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAMd,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4BAEC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eAAe,cAAc,mBACvC;4BACF,OAAO;gCACL,iBAAiB,UAAU,eAAe,uBAAuB;4BACnE;2BAPK;;;;;;;;;;8BAaX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,iBAAiB,CAAC;wBACjC,WAAU;wBACV,OAAO;4BAAC,OAAO;wBAAoB;;4BAElC,gBAAgB,aAAa;4BAAU;;;;;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 2891, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Footer.js"], "sourcesContent": ["\"use client\"\nimport React, { useState } from 'react';\n\nexport default function Footer() {\n  const [email, setEmail] = useState('');\n  const [isSubscribed, setIsSubscribed] = useState(false);\n\n  const handleSubscribe = (e) => {\n    e.preventDefault();\n    if (email) {\n      setIsSubscribed(true);\n      setEmail('');\n      setTimeout(() => setIsSubscribed(false), 3000);\n    }\n  };\n\n  return (\n    <footer className=\"w-full py-16 px-4 border-t\" style={{backgroundColor: 'var(--neutral-900)', borderColor: 'var(--neutral-800)'}}>\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12 mb-12\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center gap-3 mb-6\">\n              <div className=\"relative\">\n                <div className=\"w-10 h-10 rounded-full\" style={{backgroundColor: '#7DD3FC'}}></div>\n                <div className=\"absolute top-1 left-2 w-8 h-8 rounded-full\" style={{backgroundColor: '#A78BFA'}}></div>\n              </div>\n              <span className=\"font-bold text-2xl\" style={{color: 'var(--neutral-100)'}}>Kambaa</span>\n            </div>\n            \n            <p className=\"mb-6 leading-relaxed\" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-300)'}}>\n              Transform your audio into perfect text with Kambaa's advanced AI-powered transcription service.\n              Fast, accurate, and secure - trusted by professionals worldwide.\n            </p>\n\n            {/* Social Links */}\n            <div className=\"flex gap-4\">\n              <a href=\"#\" className=\"w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80\" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80\" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80\" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>\n                <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold mb-6\" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>\n              Navigation\n            </h3>\n            <ul className=\"space-y-4\">\n              <li>\n                <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>\n                  Home\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>\n                  Pricing\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>\n                  About\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Newsletter Subscription */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold mb-6\" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>\n              Stay Updated\n            </h3>\n            <p className=\"mb-6 leading-relaxed\" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>\n              Get the latest updates on new features, improvements, and AI transcription tips.\n            </p>\n            \n            <form onSubmit={handleSubscribe} className=\"space-y-4\">\n              <div className=\"relative\">\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email\"\n                  className=\"w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-300\"\n                  style={{\n                    backgroundColor: 'var(--neutral-800)',\n                    borderColor: 'var(--neutral-700)',\n                    color: 'var(--neutral-100)',\n                    focusRingColor: 'var(--primary-500)'\n                  }}\n                  required\n                />\n              </div>\n              \n              <button\n                type=\"submit\"\n                disabled={isSubscribed}\n                className=\"w-full btn-primary py-3 rounded-lg font-semibold transition-all duration-300 disabled:opacity-50\"\n              >\n                {isSubscribed ? '✓ Subscribed!' : 'Subscribe'}\n              </button>\n            </form>\n\n            {isSubscribed && (\n              <p className=\"mt-3 text-sm\" style={{color: 'var(--primary-400)'}}>\n                Thank you for subscribing! 🎉\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"pt-8 border-t flex flex-col md:flex-row justify-between items-center gap-4\" style={{borderColor: 'var(--neutral-800)'}}>\n          <p style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>\n            © 2024 Kambaa AI. All rights reserved.\n          </p>\n          \n          <div className=\"flex items-center gap-6\">\n            <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>\n              Terms of Service\n            </a>\n            <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>\n              Privacy Policy\n            </a>\n            <a href=\"#\" className=\"transition-colors duration-300 hover:text-primary-400\" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>\n              Cookie Policy\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,IAAI,OAAO;YACT,gBAAgB;YAChB,SAAS;YACT,WAAW,IAAM,gBAAgB,QAAQ;QAC3C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;QAA6B,OAAO;YAAC,iBAAiB;YAAsB,aAAa;QAAoB;kBAC7H,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAyB,OAAO;wDAAC,iBAAiB;oDAAS;;;;;;8DAC1E,8OAAC;oDAAI,WAAU;oDAA6C,OAAO;wDAAC,iBAAiB;oDAAS;;;;;;;;;;;;sDAEhG,8OAAC;4CAAK,WAAU;4CAAqB,OAAO;gDAAC,OAAO;4CAAoB;sDAAG;;;;;;;;;;;;8CAG7E,8OAAC;oCAAE,WAAU;oCAAuB,OAAO;wCAAC,UAAU;wCAAoB,OAAO;oCAAoB;8CAAG;;;;;;8CAMxG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAA2G,OAAO;gDAAC,iBAAiB;gDAAsB,OAAO;4CAAoB;sDACzM,cAAA,8OAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAA2G,OAAO;gDAAC,iBAAiB;gDAAsB,OAAO;4CAAoB;sDACzM,cAAA,8OAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAA2G,OAAO;gDAAC,iBAAiB;gDAAsB,OAAO;4CAAoB;sDACzM,cAAA,8OAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAqB,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;8CAGrG,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAwD,OAAO;oDAAC,UAAU;oDAAoB,OAAO;gDAAoB;0DAAG;;;;;;;;;;;sDAIpJ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAwD,OAAO;oDAAC,UAAU;oDAAoB,OAAO;gDAAoB;0DAAG;;;;;;;;;;;sDAIpJ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;gDAAwD,OAAO;oDAAC,UAAU;oDAAoB,OAAO;gDAAoB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCAQxJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAqB,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;8CAGrG,8OAAC;oCAAE,WAAU;oCAAuB,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;8CAItG,8OAAC;oCAAK,UAAU;oCAAiB,WAAU;;sDACzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,aAAY;gDACZ,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,aAAa;oDACb,OAAO;oDACP,gBAAgB;gDAClB;gDACA,QAAQ;;;;;;;;;;;sDAIZ,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,eAAe,kBAAkB;;;;;;;;;;;;gCAIrC,8BACC,8OAAC;oCAAE,WAAU;oCAAe,OAAO;wCAAC,OAAO;oCAAoB;8CAAG;;;;;;;;;;;;;;;;;;8BAQxE,8OAAC;oBAAI,WAAU;oBAA6E,OAAO;wBAAC,aAAa;oBAAoB;;sCACnI,8OAAC;4BAAE,OAAO;gCAAC,UAAU;gCAAkB,OAAO;4BAAoB;sCAAG;;;;;;sCAIrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;oCAAwD,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;8CAGhJ,8OAAC;oCAAE,MAAK;oCAAI,WAAU;oCAAwD,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;8CAGhJ,8OAAC;oCAAE,MAAK;oCAAI,WAAU;oCAAwD,OAAO;wCAAC,UAAU;wCAAkB,OAAO;oCAAoB;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5J", "debugId": null}}]}