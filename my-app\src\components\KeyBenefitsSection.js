"use client"
import React from 'react';

const benefits = [
  {
    id: 1,
    title: "AI-Powered Accuracy",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M9 12l2 2 4-4"/>
        <circle cx="12" cy="12" r="10"/>
        <path d="M8 12l2 2 4-4"/>
      </svg>
    )
  },
  {
    id: 2,
    title: "Lightning Fast",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
      </svg>
    )
  },
  {
    id: 3,
    title: "Secure & Private",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
        <circle cx="12" cy="16" r="1"/>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
      </svg>
    )
  },
  {
    id: 4,
    title: "24/7 Support",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
        <path d="M8 9h8"/>
        <path d="M8 13h6"/>
      </svg>
    )
  }
];

export default function KeyBenefitsSection() {
  return (
    <section className="w-full py-20 px-4" style={{backgroundColor: 'var(--neutral-900)'}}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            <div className="flex items-center gap-2" style={{color: 'var(--primary-400)'}}>
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span className="text-sm font-semibold uppercase tracking-wider">KEY BENEFITS</span>
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight" style={{color: 'var(--neutral-100)'}}>
              Start Enjoying Smarter Transcription{' '}
              <span style={{color: 'var(--primary-400)'}}>Today</span>
            </h2>

            <p className="text-lg md:text-xl leading-relaxed max-w-lg" style={{color: 'var(--neutral-300)'}}>
              Experience the future of audio transcription with Kambaa AI. Our advanced speech recognition technology delivers unmatched accuracy and speed, making it the perfect choice for professionals, students, and content creators who demand the best.
            </p>

            <div className="flex items-center gap-4">
              <button className="btn-secondary">
                Try Free Now
              </button>
              <div className="w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-colors duration-300 hover:bg-indigo-600" style={{backgroundColor: 'var(--primary-500)'}}>
                <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>
          
          {/* Right side - Benefits grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {benefits.map((benefit) => (
              <div
                key={benefit.id}
                className="rounded-2xl p-8 border transition-all duration-300 group hover:transform hover:scale-105 hover:border-indigo-400"
                style={{
                  backgroundColor: 'var(--neutral-800)',
                  opacity: 0.8,
                  borderColor: 'var(--neutral-700)'
                }}
              >
                <div className="mb-6 group-hover:scale-110 transition-transform duration-300" style={{color: 'var(--primary-400)'}}>
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-semibold" style={{color: 'var(--neutral-100)'}}>
                  {benefit.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
