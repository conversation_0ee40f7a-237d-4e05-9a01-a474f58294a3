import React from 'react';

const benefits = [
  {
    id: 1,
    title: "Tailored Strategy",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M9 12l2 2 4-4"/>
        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
        <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
        <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
      </svg>
    )
  },
  {
    id: 2,
    title: "Expert Team",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
        <circle cx="9" cy="7" r="4"/>
        <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
      </svg>
    )
  },
  {
    id: 3,
    title: "Data-Driven",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M12 2v20"/>
        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
        <circle cx="12" cy="12" r="3"/>
        <path d="M3 12h6"/>
        <path d="M15 12h6"/>
      </svg>
    )
  },
  {
    id: 4,
    title: "Continuous Support",
    icon: (
      <svg className="w-12 h-12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
        <circle cx="12" cy="12" r="2"/>
      </svg>
    )
  }
];

export default function KeyBenefitsSection() {
  return (
    <section className="w-full py-20 px-4 bg-gray-900">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            <div className="flex items-center gap-2 text-orange-400">
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span className="text-sm font-semibold uppercase tracking-wider">KEY BENEFITS</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Start Enjoying Smarter Marketing{' '}
              <span className="text-orange-400">Today</span>
            </h2>
            
            <p className="text-gray-300 text-lg leading-relaxed max-w-lg">
              Choosing the right marketing partner can make all the difference. At [Your Company Name], we combine creativity, strategy, and data-driven insights to deliver real results. From increasing brand awareness to boosting conversions, our team is dedicated to helping your business grow every step of the way.
            </p>
            
            <div className="flex items-center gap-4">
              <button className="px-8 py-4 bg-transparent border-2 border-gray-600 text-white font-semibold rounded-full hover:border-orange-400 hover:text-orange-400 transition-all duration-300">
                Contact Us
              </button>
              <div className="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center cursor-pointer hover:bg-orange-500 transition-colors duration-300">
                <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>
          </div>
          
          {/* Right side - Benefits grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {benefits.map((benefit) => (
              <div
                key={benefit.id}
                className="bg-gray-800/50 rounded-2xl p-8 border border-gray-700/50 hover:border-orange-400/50 transition-all duration-300 group hover:transform hover:scale-105"
              >
                <div className="text-orange-400 mb-6 group-hover:scale-110 transition-transform duration-300">
                  {benefit.icon}
                </div>
                <h3 className="text-white text-xl font-semibold">
                  {benefit.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
