"use client"
import React, { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);

const contentBlocks = [
  {
    title: "Lightning-Fast Transcription",
    desc: "Experience real-time speech-to-text conversion with industry-leading accuracy and speed. Perfect for meetings, lectures, and interviews.",
    image: "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80",
  },
  {
    title: "Seamless Collaboration",
    desc: "Share transcripts instantly with your team, add comments, and keep everyone in sync. Collaboration has never been easier.",
    image: "https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80",
  },
  {
    title: "Multi-Language Support",
    desc: "Break language barriers with support for 30+ languages and dialects. Our AI adapts to your needs, wherever you are.",
    image: "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80",
  },
  {
    title: "Enterprise-Grade Security",
    desc: "Your data is protected with end-to-end encryption and strict privacy controls. Trusted by organizations worldwide.",
    image: "https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80",
  },
];

export default function StickyImageScrollSection() {
  const [activeIdx, setActiveIdx] = useState(0);
  const contentRefs = useRef([]);
  const imageRef = useRef();
  const imageAnimRef = useRef();

  useEffect(() => {
    contentRefs.current.forEach((el, idx) => {
      if (!el) return;
      gsap.fromTo(
        el,
        { opacity: 0, y: 60 },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          scrollTrigger: {
            trigger: el,
            start: "top 70%",
            toggleActions: "play none none none",
            onEnter: () => setActiveIdx(idx),
          },
        }
      );
    });
  }, []);

  useEffect(() => {
    if (!imageAnimRef.current) return;
    gsap.fromTo(
      imageAnimRef.current,
      { opacity: 0, scale: 0.96, y: 40 },
      { opacity: 1, scale: 1, y: 0, duration: 0.7, ease: "power3.out" }
    );
  }, [activeIdx]);

  // Unified background
  const sectionBg = {
    background: "radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)"
  };

  return (
    <section className="w-full py-24 px-4" style={sectionBg}>
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row gap-12 md:gap-0 min-h-[80vh]">
        {/* Left: Content blocks */}
        <div className="flex-1 flex flex-col gap-16 justify-center">
          {contentBlocks.map((block, idx) => (
            <div
              key={idx}
              ref={el => (contentRefs.current[idx] = el)}
              className={`bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full mb-4 transition-all duration-500 ${activeIdx === idx ? 'ring-2 ring-indigo-500/40' : ''}`}
              style={{ minHeight: 180 }}
            >
              <h3 className="text-3xl md:text-4xl font-bold text-gray-100 mb-3">{block.title}</h3>
              <p className="text-gray-300 text-lg">{block.desc}</p>
            </div>
          ))}
        </div>
        {/* Right: Sticky image */}
        <div className="flex-1 flex items-center justify-center relative">
          <div
            className="sticky top-32 w-full max-w-md h-[420px] flex items-center justify-center rounded-2xl overflow-hidden shadow-2xl bg-[#181d20]/80"
            ref={imageRef}
            style={{ minHeight: 320 }}
          >
            <img
              key={activeIdx}
              ref={imageAnimRef}
              src={contentBlocks[activeIdx].image}
              alt={contentBlocks[activeIdx].title}
              className="object-cover w-full h-full transition-all duration-700"
              style={{ borderRadius: 18 }}
            />
          </div>
        </div>
      </div>
    </section>
  );
} 