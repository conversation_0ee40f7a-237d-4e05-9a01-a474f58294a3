"use client"
import React, { useState, useEffect } from 'react';

const notifications = [
  {
    id: 1,
    type: 'success',
    icon: '🎤',
    title: 'Audio transcription completed',
    message: 'Your 45-minute meeting has been transcribed with 99.8% accuracy',
    position: { top: '15%', left: '5%' },
    delay: 0
  },
  {
    id: 2,
    type: 'info',
    icon: '📊',
    title: 'New transcription request',
    message: 'Processing audio file: "Team_Meeting_Jan_2024.mp3"',
    position: { top: '25%', right: '8%' },
    delay: 1000
  },
  {
    id: 3,
    type: 'success',
    icon: '✨',
    title: 'AI Enhancement Complete',
    message: 'Speaker identification and punctuation added automatically',
    position: { bottom: '30%', left: '10%' },
    delay: 2000
  },
  {
    id: 4,
    type: 'notification',
    icon: '📝',
    title: 'Export Ready',
    message: 'Your transcript is ready for download in multiple formats',
    position: { bottom: '15%', right: '12%' },
    delay: 3000
  }
];

export default function InteractiveNotificationSection() {
  const [visibleNotifications, setVisibleNotifications] = useState([]);

  useEffect(() => {
    notifications.forEach((notification) => {
      setTimeout(() => {
        setVisibleNotifications(prev => [...prev, notification.id]);
      }, notification.delay);
    });
  }, []);

  const getNotificationStyle = (notification) => {
    const baseStyle = {
      position: 'absolute',
      transform: visibleNotifications.includes(notification.id) 
        ? 'translateY(0) scale(1)' 
        : 'translateY(20px) scale(0.9)',
      opacity: visibleNotifications.includes(notification.id) ? 1 : 0,
      transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
      ...notification.position
    };
    return baseStyle;
  };

  return (
    <section className="w-full py-20 px-4 overflow-hidden" style={{background: 'linear-gradient(135deg, var(--primary-50), var(--secondary-50))'}}>
      <div className="max-w-7xl mx-auto relative">
        <div className="flex items-center justify-center min-h-[600px]">
          {/* Central image container */}
          <div className="relative">
            {/* Main background shape */}
            <div className="w-96 h-96 rounded-3xl transform rotate-6 shadow-2xl" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}></div>

            {/* Person with laptop image */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-80 h-80 rounded-2xl flex items-center justify-center transform -rotate-6" style={{background: 'linear-gradient(135deg, var(--primary-400), var(--secondary-400))'}}>
                {/* Person illustration */}
                <div className="relative">
                  {/* Laptop */}
                  <div className="w-32 h-20 bg-gray-800 rounded-lg mb-4 relative">
                    <div className="w-30 h-18 bg-gray-100 rounded-sm m-1 flex items-center justify-center">
                      <div className="text-xs text-gray-600">🎵 → 📝</div>
                    </div>
                  </div>
                  
                  {/* Person silhouette */}
                  <div className="w-16 h-16 bg-gray-700 rounded-full mx-auto mb-2"></div>
                  <div className="w-20 h-12 bg-gray-600 rounded-t-full mx-auto"></div>
                  
                  {/* Headphones */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                    <div className="w-20 h-4 border-4 border-gray-800 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Floating notification cards */}
        {notifications.map((notification) => (
          <div
            key={notification.id}
            style={getNotificationStyle(notification)}
            className="bg-white rounded-xl shadow-lg p-4 max-w-xs border border-gray-100 hover:shadow-xl transition-shadow duration-300"
          >
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center text-white flex-shrink-0" style={{background: 'linear-gradient(135deg, var(--primary-500), var(--secondary-500))'}}>
                <span className="text-lg">{notification.icon}</span>
              </div>
              <div className="flex-1 min-w-0">
                <h4 style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-900)'}} className="font-semibold mb-1 truncate">
                  {notification.title}
                </h4>
                <p style={{fontSize: 'var(--text-xs)', color: 'var(--neutral-600)'}} className="leading-relaxed">
                  {notification.message}
                </p>
              </div>
            </div>
            
            {/* Status indicator */}
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-500">Live</span>
              </div>
              <span className="text-xs text-gray-400">Just now</span>
            </div>
          </div>
        ))}
        
        {/* Background decorative elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-orange-200 rounded-full opacity-50 animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-yellow-200 rounded-full opacity-50 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-5 w-12 h-12 bg-orange-300 rounded-full opacity-30 animate-bounce"></div>
        <div className="absolute top-1/3 right-5 w-8 h-8 bg-yellow-300 rounded-full opacity-40 animate-bounce delay-500"></div>
      </div>
    </section>
  );
}
