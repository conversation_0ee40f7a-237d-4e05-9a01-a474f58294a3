import React from "react";

const testimonials = [
  {
    text: "I've never seen a tool like this. Creating endless short videos is a breeze, and the auto-generated captions and effects are perfect. A must-have for social media!",
    name: "<PERSON>",
    role: "Social Media Influencer",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    text: "Creating short videos is effortless with this tool. It handles everything from captions to music, delivering fantastic results. A game-changer for social media!",
    name: "<PERSON>",
    role: "Content Creator",
    avatar: "https://randomuser.me/api/portraits/men/44.jpg",
  },
  {
    text: "This tool surpassed my expectations. It seamlessly manages captions and music, letting me focus on content. Efficient and user-friendly—I absolutely love it!",
    name: "<PERSON>",
    role: "Video marketing specialist",
    avatar: "https://randomuser.me/api/portraits/women/65.jpg",
  },
];

export default function TestimonialSection() {
  return (
    <section className="w-full flex flex-col items-center justify-center py-24 px-4 bg-transparent">
      {/* Badge */}
      <div className="mb-4">
        <span className="inline-block px-5 py-1 rounded-full bg-[#23272a] text-indigo-300 text-sm font-medium tracking-wide">Testimonial</span>
      </div>
      {/* Headline */}
      <h2 className="text-4xl md:text-5xl font-bold text-gray-100 text-center leading-tight mb-3">
        Our User Stories:<br />How We Made An Impact
      </h2>
      {/* Subtitle */}
      <p className="text-gray-400 text-center mb-14 max-w-2xl">
        Explore user stories that highlight how we made a significant impact and transformed user experiences in meaningful ways.
      </p>
      {/* Testimonials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-6xl mb-10">
        {testimonials.map((t, idx) => (
          <div key={idx} className="bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl p-8 flex flex-col justify-between shadow border border-gray-700/60">
            <p className="text-gray-100 text-base mb-8">{t.text}</p>
            <hr className="border-gray-700 mb-4" />
            <div className="flex items-center gap-3">
              <img src={t.avatar} alt={t.name} className="w-10 h-10 rounded-full object-cover border-2 border-gray-700" />
              <div>
                <div className="text-gray-100 font-semibold text-sm">{t.name}</div>
                <div className="text-gray-400 text-xs">{t.role}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Navigation Arrows */}
      <div className="flex gap-4">
        <button className="w-10 h-10 flex items-center justify-center rounded-full border border-gray-700 bg-[#181d20] text-gray-300 hover:bg-[#23272a] transition">
          <span className="text-2xl">&#8592;</span>
        </button>
        <button className="w-10 h-10 flex items-center justify-center rounded-full border border-gray-700 bg-[#181d20] text-gray-300 hover:bg-[#23272a] transition">
          <span className="text-2xl">&#8594;</span>
        </button>
      </div>
    </section>
  );
} 