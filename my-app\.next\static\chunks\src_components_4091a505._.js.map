{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyAnalyticsSection.js"], "sourcesContent": ["\"use client\"\nimport React, { useRef, useEffect, useState } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\ngsap.registerPlugin(ScrollTrigger);\n\nconst analyticsFeatures = [\n  {\n    id: 1,\n    title: \"Reach the Right Audiences\",\n    description: \"Connect with the people who matter most. Our platform helps you target and engage the right audiences with precision.\",\n    icon: \"👥\",\n    bgColor: \"bg-blue-50\",\n    iconBg: \"bg-blue-100\"\n  },\n  {\n    id: 2,\n    title: \"Real-Time Performance Tracking\",\n    description: \"Monitor your campaigns and content performance in real-time. Get instant insights and make data-driven decisions faster.\",\n    icon: \"📊\",\n    bgColor: \"bg-green-50\",\n    iconBg: \"bg-green-100\"\n  },\n  {\n    id: 3,\n    title: \"Advanced Analytics Dashboard\",\n    description: \"Comprehensive analytics dashboard with detailed metrics, custom reports, and actionable insights for better ROI.\",\n    icon: \"📈\",\n    bgColor: \"bg-purple-50\",\n    iconBg: \"bg-purple-100\"\n  },\n  {\n    id: 4,\n    title: \"Multi-Platform Integration\",\n    description: \"Seamlessly integrate with all major social media platforms and marketing tools. Centralize your analytics in one place.\",\n    icon: \"🔗\",\n    bgColor: \"bg-orange-50\",\n    iconBg: \"bg-orange-100\"\n  }\n];\n\nexport default function StickyAnalyticsSection() {\n  const [activeIdx, setActiveIdx] = useState(0);\n  const contentRefs = useRef([]);\n  const sectionRef = useRef();\n\n  useEffect(() => {\n    contentRefs.current.forEach((el, idx) => {\n      if (!el) return;\n      \n      ScrollTrigger.create({\n        trigger: el,\n        start: \"top center\",\n        end: \"bottom center\",\n        onEnter: () => setActiveIdx(idx),\n        onEnterBack: () => setActiveIdx(idx),\n      });\n    });\n\n    return () => {\n      ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n    };\n  }, []);\n\n  return (\n    <section ref={sectionRef} className=\"w-full py-24 px-4 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Left side - Sticky content */}\n          <div className=\"lg:sticky lg:top-32 lg:h-fit\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\n              We make it easy to track all analytics\n            </h2>\n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              We make tracking your analytics simple and stress-free. Get clear, real-time insights in one place and make smarter decisions without the complexity.\n            </p>\n            <button className=\"px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300\">\n              Get Started Now\n            </button>\n          </div>\n          \n          {/* Right side - Scrollable feature boxes */}\n          <div className=\"space-y-8\">\n            {analyticsFeatures.map((feature, idx) => (\n              <div\n                key={feature.id}\n                ref={el => (contentRefs.current[idx] = el)}\n                className={`${feature.bgColor} rounded-2xl p-8 border-2 transition-all duration-500 ${\n                  activeIdx === idx ? 'border-gray-300 shadow-lg scale-105' : 'border-transparent'\n                }`}\n                style={{ minHeight: '300px' }}\n              >\n                <div className=\"flex items-start gap-6\">\n                  <div className={`${feature.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center text-2xl flex-shrink-0`}>\n                    {feature.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-700 text-lg leading-relaxed\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n                \n                {/* Visual representation based on feature */}\n                <div className=\"mt-8\">\n                  {idx === 0 && (\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"relative\">\n                        <div className=\"w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-lg\">\n                          <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center\">\n                            <span className=\"text-white text-2xl\">👤</span>\n                          </div>\n                        </div>\n                        <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full\"></div>\n                        <div className=\"absolute -bottom-2 -left-2 w-6 h-6 bg-blue-300 rounded-full\"></div>\n                        <div className=\"absolute top-8 -left-8 w-4 h-4 bg-blue-200 rounded-full\"></div>\n                      </div>\n                    </div>\n                  )}\n                  \n                  {idx === 1 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"flex items-end justify-between h-20\">\n                        <div className=\"w-8 bg-green-300 rounded-t\" style={{height: '40%'}}></div>\n                        <div className=\"w-8 bg-green-400 rounded-t\" style={{height: '60%'}}></div>\n                        <div className=\"w-8 bg-green-500 rounded-t\" style={{height: '80%'}}></div>\n                        <div className=\"w-8 bg-green-600 rounded-t\" style={{height: '100%'}}></div>\n                        <div className=\"w-8 bg-green-400 rounded-t\" style={{height: '70%'}}></div>\n                      </div>\n                    </div>\n                  )}\n                  \n                  {idx === 2 && (\n                    <div className=\"bg-white rounded-lg p-4\">\n                      <div className=\"grid grid-cols-3 gap-4\">\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-purple-600\">2.4K</div>\n                          <div className=\"text-sm text-gray-600\">Views</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-purple-600\">89%</div>\n                          <div className=\"text-sm text-gray-600\">Engagement</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-purple-600\">156</div>\n                          <div className=\"text-sm text-gray-600\">Conversions</div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                  \n                  {idx === 3 && (\n                    <div className=\"flex justify-center gap-4\">\n                      <div className=\"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold\">f</div>\n                      <div className=\"w-12 h-12 bg-pink-500 rounded-lg flex items-center justify-center text-white font-bold\">ig</div>\n                      <div className=\"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white font-bold\">tw</div>\n                      <div className=\"w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold\">yt</div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,YAAY,OAAO,CAAC,OAAO;oDAAC,CAAC,IAAI;oBAC/B,IAAI,CAAC,IAAI;oBAET,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACnB,SAAS;wBACT,OAAO;wBACP,KAAK;wBACL,OAAO;gEAAE,IAAM,aAAa;;wBAC5B,WAAW;gEAAE,IAAM,aAAa;;oBAClC;gBACF;;YAEA;oDAAO;oBACL,wIAAA,CAAA,gBAAa,CAAC,MAAM,GAAG,OAAO;4DAAC,CAAA,UAAW,QAAQ,IAAI;;gBACxD;;QACF;2CAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAGhF,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,6LAAC;gCAAO,WAAU;0CAAuG;;;;;;;;;;;;kCAM3H,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,oBAC/B,6LAAC;gCAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;gCACvC,WAAW,AAAC,GACV,OADY,QAAQ,OAAO,EAAC,0DAE7B,OADC,cAAc,MAAM,wCAAwC;gCAE9D,OAAO;oCAAE,WAAW;gCAAQ;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,GAAiB,OAAf,QAAQ,MAAM,EAAC;0DAC/B,QAAQ,IAAI;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;kDAM1B,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,mBACP,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;4CAKpB,QAAQ,mBACP,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEACjE,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEACjE,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEACjE,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAAC,QAAQ;4DAAM;;;;;;sEAClE,6LAAC;4DAAI,WAAU;4DAA6B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;;;;;;;;;;;;4CAKtE,QAAQ,mBACP,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;4CAM9C,QAAQ,mBACP,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyF;;;;;;kEACxG,6LAAC;wDAAI,WAAU;kEAAyF;;;;;;kEACxG,6LAAC;wDAAI,WAAU;kEAAyF;;;;;;kEACxG,6LAAC;wDAAI,WAAU;kEAAwF;;;;;;;;;;;;;;;;;;;+BA1ExG,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAqF/B;GAjIwB;KAAA", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/ZigzagAnimatedSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst slides = [\r\n  {\r\n    title: \"Enterprise-Ready Security\",\r\n    desc: \"Your audio and transcripts are protected with industry-leading encryption and privacy controls. Trusted by top organizations worldwide.\",\r\n    cta: \"Learn More\",\r\n    logos: [\r\n      { name: \"<PERSON>\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"#23272a\"/><ellipse cx=\"20\" cy=\"20\" rx=\"12\" ry=\"8\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Epicurious\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"5\" y=\"15\" width=\"30\" height=\"10\" rx=\"5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"25\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#23272a\"/></svg> },\r\n      { name: \"GlobalBank\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><polygon points=\"20,5 35,35 5,35\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"25\" r=\"5\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><circle cx=\"160\" cy=\"160\" r=\"120\" fill=\"#23272a\"/><rect x=\"100\" y=\"140\" width=\"120\" height=\"40\" rx=\"20\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"160\" width=\"80\" height=\"20\" rx=\"10\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Loved by Creators\",\r\n    desc: \"Content creators, podcasters, and educators rely on our AI to turn their voice into polished, shareable text. Join a global community of storytellers.\",\r\n    cta: \"See User Stories\",\r\n    logos: [\r\n      { name: \"Catalog\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"20\" r=\"10\" fill=\"#23272a\"/></svg> },\r\n      { name: \"Luminous\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"10\" y=\"10\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"20\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"30\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/></svg> },\r\n      { name: \"Quotient\", svg: <svg width=\"36\" height=\"36\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><rect x=\"18\" y=\"10\" width=\"4\" height=\"20\" rx=\"2\" fill=\"#23272a\"/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"60\" y=\"60\" width=\"200\" height=\"200\" rx=\"40\" fill=\"#6366f1\" opacity=\"0.7\" /><circle cx=\"160\" cy=\"160\" r=\"60\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Seamless Integrations\",\r\n    desc: \"Connect with your favorite tools—export transcripts to Google Docs, Slack, Notion, and more. Workflows that fit your needs.\",\r\n    cta: \"Explore Integrations\",\r\n    logos: [\r\n      { name: \"Slack\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><rect x='10' y='18' width='20' height='4' rx='2' fill='#23272a'/></svg> },\r\n      { name: \"Notion\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Google Docs\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><rect x=\"100\" y=\"100\" width=\"120\" height=\"120\" rx=\"30\" fill=\"#6366f1\" opacity=\"0.7\" /><rect x=\"120\" y=\"120\" width=\"80\" height=\"80\" rx=\"20\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Global Language Support\",\r\n    desc: \"Transcribe and translate in 30+ languages. Our AI breaks down barriers and connects you to the world.\",\r\n    cta: \"See Supported Languages\",\r\n    logos: [\r\n      { name: \"World\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='18' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='10' ry='6' fill='#23272a'/></svg> },\r\n      { name: \"Translate\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },\r\n      { name: \"Globe\", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='12' ry='8' fill='#23272a'/></svg> },\r\n    ],\r\n    animation: (\r\n      <svg width=\"320\" height=\"320\" viewBox=\"0 0 320 320\" fill=\"none\"><ellipse cx=\"160\" cy=\"160\" rx=\"120\" ry=\"80\" fill=\"#6366f1\" opacity=\"0.7\" /><ellipse cx=\"160\" cy=\"160\" rx=\"60\" ry=\"40\" fill=\"#b0b6c1\" opacity=\"0.5\" /></svg>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function ZigzagAnimatedSection() {\r\n  const rowRefs = useRef([]);\r\n\r\n  useEffect(() => {\r\n    rowRefs.current.forEach((el, i) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 100, x: i % 2 === 0 ? -100 : 100, scale: 0.95 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          x: 0,\r\n          scale: 1,\r\n          duration: 1.1,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 85%\",\r\n            toggleActions: \"play none none none\",\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  // Use the same radial gradient as the Hero/global background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full\" style={sectionBg}>\r\n      {slides.map((slide, i) => (\r\n        <div\r\n          key={i}\r\n          ref={el => (rowRefs.current[i] = el)}\r\n          className={`relative flex flex-col md:flex-row items-center justify-center min-h-screen py-12 md:py-0 px-4 md:px-16 gap-10 md:gap-0`}\r\n        >\r\n          {/* Context */}\r\n          <div className={`flex-1 flex flex-col justify-center items-${i % 2 === 0 ? 'start' : 'end'} z-10`}>\r\n            <div className=\"bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full backdrop-blur-md\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold text-gray-100 mb-4 leading-tight\">{slide.title}</h2>\r\n              <p className=\"text-gray-300 text-lg mb-6\">{slide.desc}</p>\r\n              <div className=\"flex flex-wrap gap-4 mb-6\">\r\n                {slide.logos.map((logo, idx) => (\r\n                  <div key={logo.name + idx} className=\"flex items-center gap-2 bg-[#23272a]/80 rounded-lg px-3 py-2 shadow\">\r\n                    {logo.svg}\r\n                    <span className=\"text-gray-200 font-semibold text-base\">{logo.name}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <button className=\"mt-2 px-6 py-2 rounded-full bg-indigo-600 text-white font-semibold shadow hover:bg-indigo-500 transition\">{slide.cta}</button>\r\n            </div>\r\n          </div>\r\n          {/* Animation */}\r\n          <div className=\"flex-1 flex items-center justify-center z-10\">\r\n            <div className=\"rounded-2xl shadow-2xl bg-[#181d20]/80 p-6 md:p-12 flex items-center justify-center\">\r\n              {slide.animation}\r\n            </div>\r\n          </div>\r\n          {/* Decorative overlay for depth */}\r\n          <div className=\"absolute inset-0 pointer-events-none z-0\">\r\n            <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1440 900\" fill=\"none\" className=\"w-full h-full\">\r\n              <defs>\r\n                <radialGradient id={`glow${i}`} cx=\"50%\" cy=\"50%\" r=\"70%\">\r\n                  <stop offset=\"0%\" stopColor=\"#6366f1\" stopOpacity=\"0.08\" />\r\n                  <stop offset=\"100%\" stopColor=\"transparent\" />\r\n                </radialGradient>\r\n              </defs>\r\n              <rect width=\"1440\" height=\"900\" fill={`url(#glow${i})`} />\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,SAAS;IACb;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAClM;gBAAE,MAAM;gBAAc,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YAClO;gBAAE,MAAM;gBAAc,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAQ,QAAO;4BAAkB,MAAK;;;;;;sCAAW,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC/L;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAM,MAAK;;;;;;8BAAW,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEvR;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAW,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;;;;;;;YAAkB;YACzL;gBAAE,MAAM;gBAAY,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAM,MAAK;;;;;;;;;;;;YAAkB;YACrS;gBAAE,MAAM;gBAAY,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAI,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAC7M;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEhN;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAI,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YACzM;gBAAE,MAAM;gBAAU,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC5N;gBAAE,MAAM;gBAAe,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SAClO;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAM,QAAO;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAK,GAAE;oBAAM,GAAE;oBAAM,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEtO;IACA;QACE,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;YACL;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAChM;gBAAE,MAAM;gBAAa,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAK,GAAE;4BAAI,GAAE;4BAAI,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;sCAAW,6LAAC;4BAAK,GAAE;4BAAK,GAAE;4BAAK,OAAM;4BAAK,QAAO;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;YAC/N;gBAAE,MAAM;gBAAS,mBAAK,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;;sCAAO,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,MAAK;;;;;;sCAAW,6LAAC;4BAAQ,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,MAAK;;;;;;;;;;;;YAAkB;SACjM;QACD,yBACE,6LAAC;YAAI,OAAM;YAAM,QAAO;YAAM,SAAQ;YAAc,MAAK;;8BAAO,6LAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;8BAAQ,6LAAC;oBAAQ,IAAG;oBAAM,IAAG;oBAAM,IAAG;oBAAK,IAAG;oBAAK,MAAK;oBAAU,SAAQ;;;;;;;;;;;;IAEjN;CACD;AAEc,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,QAAQ,OAAO,CAAC,OAAO;mDAAC,CAAC,IAAI;oBAC3B,IAAI,CAAC,IAAI;oBACT,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;wBAAE,SAAS;wBAAG,GAAG;wBAAK,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM;wBAAK,OAAO;oBAAK,GAC/D;wBACE,SAAS;wBACT,GAAG;wBACH,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBAEJ;;QACF;0CAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAS,OAAO;kBAChC,OAAO,GAAG,CAAC,CAAC,OAAO,kBAClB,6LAAC;gBAEC,KAAK,CAAA,KAAO,QAAQ,OAAO,CAAC,EAAE,GAAG;gBACjC,WAAY;;kCAGZ,6LAAC;wBAAI,WAAW,AAAC,6CAA0E,OAA9B,IAAI,MAAM,IAAI,UAAU,OAAM;kCACzF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmE,MAAM,KAAK;;;;;;8CAC5F,6LAAC;oCAAE,WAAU;8CAA8B,MAAM,IAAI;;;;;;8CACrD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACtB,6LAAC;4CAA0B,WAAU;;gDAClC,KAAK,GAAG;8DACT,6LAAC;oDAAK,WAAU;8DAAyC,KAAK,IAAI;;;;;;;2CAF1D,KAAK,IAAI,GAAG;;;;;;;;;;8CAM1B,6LAAC;oCAAO,WAAU;8CAA4G,MAAM,GAAG;;;;;;;;;;;;;;;;;kCAI3I,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,SAAS;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAAO,QAAO;4BAAO,SAAQ;4BAAe,MAAK;4BAAO,WAAU;;8CAC3E,6LAAC;8CACC,cAAA,6LAAC;wCAAe,IAAI,AAAC,OAAQ,OAAF;wCAAK,IAAG;wCAAM,IAAG;wCAAM,GAAE;;0DAClD,6LAAC;gDAAK,QAAO;gDAAK,WAAU;gDAAU,aAAY;;;;;;0DAClD,6LAAC;gDAAK,QAAO;gDAAO,WAAU;;;;;;;;;;;;;;;;;8CAGlC,6LAAC;oCAAK,OAAM;oCAAO,QAAO;oCAAM,MAAM,AAAC,YAAa,OAAF,GAAE;;;;;;;;;;;;;;;;;;eAnCnD;;;;;;;;;;AA0Cf;GA7EwB;KAAA", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyImageScrollSection.js"], "sourcesContent": ["\"use client\"\r\nimport React, { useRef, useEffect, useState } from \"react\";\r\nimport { gsap } from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\r\ngsap.registerPlugin(ScrollTrigger);\r\n\r\nconst contentBlocks = [\r\n  {\r\n    title: \"Lightning-Fast Transcription\",\r\n    desc: \"Experience real-time speech-to-text conversion with industry-leading accuracy and speed. Perfect for meetings, lectures, and interviews.\",\r\n    image: \"https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Seamless Collaboration\",\r\n    desc: \"Share transcripts instantly with your team, add comments, and keep everyone in sync. Collaboration has never been easier.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Multi-Language Support\",\r\n    desc: \"Break language barriers with support for 30+ languages and dialects. Our AI adapts to your needs, wherever you are.\",\r\n    image: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n  {\r\n    title: \"Enterprise-Grade Security\",\r\n    desc: \"Your data is protected with end-to-end encryption and strict privacy controls. Trusted by organizations worldwide.\",\r\n    image: \"https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80\",\r\n  },\r\n];\r\n\r\nexport default function StickyImageScrollSection() {\r\n  const [activeIdx, setActiveIdx] = useState(0);\r\n  const contentRefs = useRef([]);\r\n  const imageRef = useRef();\r\n  const imageAnimRef = useRef();\r\n\r\n  useEffect(() => {\r\n    contentRefs.current.forEach((el, idx) => {\r\n      if (!el) return;\r\n      gsap.fromTo(\r\n        el,\r\n        { opacity: 0, y: 60 },\r\n        {\r\n          opacity: 1,\r\n          y: 0,\r\n          duration: 0.8,\r\n          ease: \"power3.out\",\r\n          scrollTrigger: {\r\n            trigger: el,\r\n            start: \"top 70%\",\r\n            toggleActions: \"play none none none\",\r\n            onEnter: () => setActiveIdx(idx),\r\n          },\r\n        }\r\n      );\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!imageAnimRef.current) return;\r\n    gsap.fromTo(\r\n      imageAnimRef.current,\r\n      { opacity: 0, scale: 0.96, y: 40 },\r\n      { opacity: 1, scale: 1, y: 0, duration: 0.7, ease: \"power3.out\" }\r\n    );\r\n  }, [activeIdx]);\r\n\r\n  // Unified background\r\n  const sectionBg = {\r\n    background: \"radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)\"\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full py-24 px-4\" style={sectionBg}>\r\n      <div className=\"max-w-7xl mx-auto flex flex-col md:flex-row gap-12 md:gap-0 min-h-[80vh]\">\r\n        {/* Left: Content blocks */}\r\n        <div className=\"flex-1 flex flex-col gap-16 justify-center\">\r\n          {contentBlocks.map((block, idx) => (\r\n            <div\r\n              key={idx}\r\n              ref={el => (contentRefs.current[idx] = el)}\r\n              className={`bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full mb-4 transition-all duration-500 ${activeIdx === idx ? 'ring-2 ring-indigo-500/40' : ''}`}\r\n              style={{ minHeight: 180 }}\r\n            >\r\n              <h3 className=\"text-3xl md:text-4xl font-bold text-gray-100 mb-3\">{block.title}</h3>\r\n              <p className=\"text-gray-300 text-lg\">{block.desc}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        {/* Right: Sticky image */}\r\n        <div className=\"flex-1 flex items-center justify-center relative\">\r\n          <div\r\n            className=\"sticky top-32 w-full max-w-md h-[420px] flex items-center justify-center rounded-2xl overflow-hidden shadow-2xl bg-[#181d20]/80\"\r\n            ref={imageRef}\r\n            style={{ minHeight: 320 }}\r\n          >\r\n            <img\r\n              key={activeIdx}\r\n              ref={imageAnimRef}\r\n              src={contentBlocks[activeIdx].image}\r\n              alt={contentBlocks[activeIdx].title}\r\n              className=\"object-cover w-full h-full transition-all duration-700\"\r\n              style={{ borderRadius: 18 }}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAIA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAEjC,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC7B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,YAAY,OAAO,CAAC,OAAO;sDAAC,CAAC,IAAI;oBAC/B,IAAI,CAAC,IAAI;oBACT,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,IACA;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;4BACf,OAAO;sEAAE,IAAM,aAAa;;wBAC9B;oBACF;gBAEJ;;QACF;6CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,IAAI,CAAC,aAAa,OAAO,EAAE;YAC3B,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,aAAa,OAAO,EACpB;gBAAE,SAAS;gBAAG,OAAO;gBAAM,GAAG;YAAG,GACjC;gBAAE,SAAS;gBAAG,OAAO;gBAAG,GAAG;gBAAG,UAAU;gBAAK,MAAM;YAAa;QAEpE;6CAAG;QAAC;KAAU;IAEd,qBAAqB;IACrB,MAAM,YAAY;QAChB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAQ,WAAU;QAAoB,OAAO;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,oBACzB,6LAAC;4BAEC,KAAK,CAAA,KAAO,YAAY,OAAO,CAAC,IAAI,GAAG;4BACvC,WAAW,AAAC,+FAAmJ,OAArD,cAAc,MAAM,8BAA8B;4BAC5J,OAAO;gCAAE,WAAW;4BAAI;;8CAExB,6LAAC;oCAAG,WAAU;8CAAqD,MAAM,KAAK;;;;;;8CAC9E,6LAAC;oCAAE,WAAU;8CAAyB,MAAM,IAAI;;;;;;;2BAN3C;;;;;;;;;;8BAWX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,KAAK;wBACL,OAAO;4BAAE,WAAW;wBAAI;kCAExB,cAAA,6LAAC;4BAEC,KAAK;4BACL,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,KAAK,aAAa,CAAC,UAAU,CAAC,KAAK;4BACnC,WAAU;4BACV,OAAO;gCAAE,cAAc;4BAAG;2BALrB;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnB;GA/EwB;KAAA", "debugId": null}}]}