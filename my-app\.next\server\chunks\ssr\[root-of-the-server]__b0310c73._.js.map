{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Navbar.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Navbar.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/Hero.js"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function Hero() {\r\n  return (\r\n    <section className=\"relative min-h-screen flex items-center px-4 overflow-hidden\">\r\n      {/* Animated background elements */}\r\n      <div className=\"absolute inset-0 pointer-events-none\">\r\n        <div className=\"absolute top-20 left-10 w-32 h-32 rounded-full blur-xl animate-pulse\" style={{backgroundColor: 'var(--primary-500)', opacity: 0.1}}></div>\r\n        <div className=\"absolute bottom-20 right-10 w-48 h-48 rounded-full blur-xl animate-pulse delay-1000\" style={{backgroundColor: 'var(--secondary-500)', opacity: 0.1}}></div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative z-10 pt-20\">\r\n        {/* Left side - Content */}\r\n        <div className=\"space-y-8\">\r\n          <h1 style={{fontSize: 'var(--text-5xl)', color: 'var(--neutral-100)'}}>\r\n            Transform Your Voice Into Perfect Text with AI\r\n          </h1>\r\n\r\n          <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-300)'}} className=\"leading-relaxed max-w-lg\">\r\n            Experience lightning-fast, AI-powered speech-to-text conversion with 99.9% accuracy. Perfect for meetings, interviews, podcasts, and content creation.\r\n          </p>\r\n\r\n          <div className=\"flex flex-col sm:flex-row gap-4\">\r\n            <button className=\"btn-primary\">\r\n              Start Transcribing Free\r\n            </button>\r\n            <button className=\"btn-secondary\">\r\n              Watch Demo\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right side - Audio Transcription Dashboard */}\r\n        <div className=\"relative\">\r\n          <div className=\"bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500\">\r\n            {/* Dashboard header */}\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"w-8 h-8 rounded-lg flex items-center justify-center\" style={{backgroundColor: 'var(--primary-600)'}}>\r\n                  <span className=\"text-white text-sm\">🎤</span>\r\n                </div>\r\n                <span className=\"font-semibold\" style={{color: 'var(--neutral-800)'}}>ZINLE AI</span>\r\n              </div>\r\n              <div className=\"flex gap-2\">\r\n                <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\r\n                <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\r\n                <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Dashboard content */}\r\n            <div className=\"space-y-4\">\r\n              {/* Stats cards */}\r\n              <div className=\"grid grid-cols-3 gap-4\">\r\n                <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                  <div className=\"text-2xl font-bold text-gray-800\">99.9%</div>\r\n                  <div className=\"text-sm text-gray-600\">Accuracy</div>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                  <div className=\"text-2xl font-bold text-gray-800\">2.4s</div>\r\n                  <div className=\"text-sm text-gray-600\">Avg Speed</div>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                  <div className=\"text-2xl font-bold text-gray-800\">50+</div>\r\n                  <div className=\"text-sm text-gray-600\">Languages</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Waveform visualization */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4 h-32\">\r\n                <div className=\"flex items-end justify-between h-full gap-1\">\r\n                  <div className=\"w-2 bg-indigo-300 rounded-t\" style={{height: '40%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-500 rounded-t\" style={{height: '70%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-600 rounded-t\" style={{height: '100%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-400 rounded-t\" style={{height: '60%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-500 rounded-t\" style={{height: '85%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-300 rounded-t\" style={{height: '45%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-600 rounded-t\" style={{height: '90%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-400 rounded-t\" style={{height: '55%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-500 rounded-t\" style={{height: '75%'}}></div>\r\n                  <div className=\"w-2 bg-indigo-300 rounded-t\" style={{height: '35%'}}></div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Transcription preview */}\r\n              <div className=\"flex gap-4\">\r\n                <div className=\"w-1/3 space-y-2\">\r\n                  <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n                  <div className=\"h-3 bg-indigo-200 rounded\"></div>\r\n                  <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n                  <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <div className=\"h-16 bg-gray-100 rounded-lg flex items-center px-3\">\r\n                    <span className=\"text-xs text-gray-600\">\"Hello, this is a live transcription...\"</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Floating elements */}\r\n          <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-indigo-400 rounded-full flex items-center justify-center animate-bounce\">\r\n            <span className=\"text-2xl\">🎵</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuE,OAAO;4BAAC,iBAAiB;4BAAsB,SAAS;wBAAG;;;;;;kCACjJ,8OAAC;wBAAI,WAAU;wBAAsF,OAAO;4BAAC,iBAAiB;4BAAwB,SAAS;wBAAG;;;;;;;;;;;;0BAGpK,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,OAAO;oCAAC,UAAU;oCAAmB,OAAO;gCAAoB;0CAAG;;;;;;0CAIvE,8OAAC;gCAAE,OAAO;oCAAC,UAAU;oCAAkB,OAAO;gCAAoB;gCAAG,WAAU;0CAA2B;;;;;;0CAI1G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAc;;;;;;kDAGhC,8OAAC;wCAAO,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAOtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAsD,OAAO;4DAAC,iBAAiB;wDAAoB;kEAChH,cAAA,8OAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;kEAEvC,8OAAC;wDAAK,WAAU;wDAAgB,OAAO;4DAAC,OAAO;wDAAoB;kEAAG;;;;;;;;;;;;0DAExE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAKnB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;0EAClD,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAM;;;;;;sEACnE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;sEAClE,8OAAC;4DAAI,WAAU;4DAA8B,OAAO;gEAAC,QAAQ;4DAAK;;;;;;;;;;;;;;;;;0DAKtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StatsSection.js"], "sourcesContent": ["import React from 'react';\n\nconst stats = [\n  { number: \"10M+\", label: \"Hours Transcribed\" },\n  { number: \"99.9%\", label: \"Accuracy Rate\" },\n  { number: \"50+\", label: \"Languages Supported\" }\n];\n\nexport default function StatsSection() {\n  return (\n    <section className=\"w-full py-20 px-4\" style={{background: 'linear-gradient(135deg, var(--primary-600), var(--secondary-600))'}}>\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8 items-center\">\n          {/* Left side - Text content */}\n          <div className=\"lg:col-span-1\">\n            <h2 style={{fontSize: 'var(--text-3xl)', color: 'white'}} className=\"font-bold leading-tight\">\n              Over 100k+ users trust ZINLE AI for transcription.\n            </h2>\n          </div>\n\n          {/* Right side - Stats */}\n          <div className=\"lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {stats.map((stat, idx) => (\n              <div key={idx} className=\"text-center lg:text-left\">\n                <div style={{fontSize: 'var(--text-5xl)'}} className=\"font-bold text-white mb-2\">\n                  {stat.number}\n                </div>\n                <div style={{fontSize: 'var(--text-lg)'}} className=\"text-white/90 font-medium\">\n                  {stat.label}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,QAAQ;IACZ;QAAE,QAAQ;QAAQ,OAAO;IAAoB;IAC7C;QAAE,QAAQ;QAAS,OAAO;IAAgB;IAC1C;QAAE,QAAQ;QAAO,OAAO;IAAsB;CAC/C;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;QAAoB,OAAO;YAAC,YAAY;QAAmE;kBAC5H,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,OAAO;gCAAC,UAAU;gCAAmB,OAAO;4BAAO;4BAAG,WAAU;sCAA0B;;;;;;;;;;;kCAMhG,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gCAAc,WAAU;;kDACvB,8OAAC;wCAAI,OAAO;4CAAC,UAAU;wCAAiB;wCAAG,WAAU;kDAClD,KAAK,MAAM;;;;;;kDAEd,8OAAC;wCAAI,OAAO;4CAAC,UAAU;wCAAgB;wCAAG,WAAU;kDACjD,KAAK,KAAK;;;;;;;+BALL;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxB", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyAnalyticsSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StickyAnalyticsSection.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StickyAnalyticsSection.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyAnalyticsSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StickyAnalyticsSection.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StickyAnalyticsSection.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/KeyBenefitsSection.js"], "sourcesContent": ["import React from 'react';\n\nconst benefits = [\n  {\n    id: 1,\n    title: \"AI-Powered Accuracy\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <path d=\"M9 12l2 2 4-4\"/>\n        <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n        <path d=\"M8 12l2 2 4-4\"/>\n      </svg>\n    )\n  },\n  {\n    id: 2,\n    title: \"Lightning Fast\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <polygon points=\"13 2 3 14 12 14 11 22 21 10 12 10 13 2\"/>\n      </svg>\n    )\n  },\n  {\n    id: 3,\n    title: \"Secure & Private\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\"/>\n        <circle cx=\"12\" cy=\"16\" r=\"1\"/>\n        <path d=\"M7 11V7a5 5 0 0 1 10 0v4\"/>\n      </svg>\n    )\n  },\n  {\n    id: 4,\n    title: \"24/7 Support\",\n    icon: (\n      <svg className=\"w-12 h-12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n        <path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"/>\n        <path d=\"M8 9h8\"/>\n        <path d=\"M8 13h6\"/>\n      </svg>\n    )\n  }\n];\n\nexport default function KeyBenefitsSection() {\n  return (\n    <section className=\"w-full py-20 px-4 bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Left side - Content */}\n          <div className=\"space-y-8\">\n            <div className=\"flex items-center gap-2 text-indigo-400\">\n              <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n              <span className=\"text-sm font-semibold uppercase tracking-wider\">KEY BENEFITS</span>\n            </div>\n\n            <h2 style={{fontSize: 'var(--text-4xl)', color: 'white'}} className=\"font-bold leading-tight\">\n              Start Enjoying Smarter Transcription{' '}\n              <span style={{color: 'var(--primary-400)'}}>Today</span>\n            </h2>\n\n            <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-300)'}} className=\"leading-relaxed max-w-lg\">\n              Experience the future of audio transcription with ZINLE AI. Our advanced speech recognition technology delivers unmatched accuracy and speed, making it the perfect choice for professionals, students, and content creators who demand the best.\n            </p>\n\n            <div className=\"flex items-center gap-4\">\n              <button className=\"btn-secondary\">\n                Try Free Now\n              </button>\n              <div className=\"w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-colors duration-300\" style={{backgroundColor: 'var(--primary-500)'}} onMouseEnter={(e) => e.target.style.backgroundColor = 'var(--primary-600)'} onMouseLeave={(e) => e.target.style.backgroundColor = 'var(--primary-500)'}>\n                <svg className=\"w-6 h-6 text-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M8 5v14l11-7z\"/>\n                </svg>\n              </div>\n            </div>\n          </div>\n          \n          {/* Right side - Benefits grid */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n            {benefits.map((benefit) => (\n              <div\n                key={benefit.id}\n                className=\"rounded-2xl p-8 border transition-all duration-300 group hover:transform hover:scale-105\"\n                style={{\n                  backgroundColor: 'var(--neutral-800)',\n                  opacity: 0.8,\n                  borderColor: 'var(--neutral-700)'\n                }}\n                onMouseEnter={(e) => e.target.style.borderColor = 'var(--primary-500)'}\n                onMouseLeave={(e) => e.target.style.borderColor = 'var(--neutral-700)'}\n              >\n                <div className=\"mb-6 group-hover:scale-110 transition-transform duration-300\" style={{color: 'var(--primary-400)'}}>\n                  {benefit.icon}\n                </div>\n                <h3 style={{fontSize: 'var(--text-xl)', color: 'white'}} className=\"font-semibold\">\n                  {benefit.title}\n                </h3>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;;;;;;8BAC1B,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;sBAC3F,cAAA,8OAAC;gBAAQ,QAAO;;;;;;;;;;;IAGtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAI,IAAG;;;;;;8BACpD,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;;;;;;8BAC1B,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;IACA;QACE,IAAI;QACJ,OAAO;QACP,oBACE,8OAAC;YAAI,WAAU;YAAY,SAAQ;YAAY,MAAK;YAAO,QAAO;YAAe,aAAY;;8BAC3F,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;8BACR,8OAAC;oBAAK,GAAE;;;;;;;;;;;;IAGd;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAU,SAAQ;wCAAY,MAAK;kDAChD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC;gCAAG,OAAO;oCAAC,UAAU;oCAAmB,OAAO;gCAAO;gCAAG,WAAU;;oCAA0B;oCACvD;kDACrC,8OAAC;wCAAK,OAAO;4CAAC,OAAO;wCAAoB;kDAAG;;;;;;;;;;;;0CAG9C,8OAAC;gCAAE,OAAO;oCAAC,UAAU;oCAAkB,OAAO;gCAAoB;gCAAG,WAAU;0CAA2B;;;;;;0CAI1G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAgB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;wCAAwG,OAAO;4CAAC,iBAAiB;wCAAoB;wCAAG,cAAc,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;wCAAsB,cAAc,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;kDACvS,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,SAAQ;4CAAY,MAAK;sDAC3D,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,SAAS;oCACT,aAAa;gCACf;gCACA,cAAc,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;gCAClD,cAAc,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;;kDAElD,8OAAC;wCAAI,WAAU;wCAA+D,OAAO;4CAAC,OAAO;wCAAoB;kDAC9G,QAAQ,IAAI;;;;;;kDAEf,8OAAC;wCAAG,OAAO;4CAAC,UAAU;4CAAkB,OAAO;wCAAO;wCAAG,WAAU;kDAChE,QAAQ,KAAK;;;;;;;+BAdX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB/B", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/InteractiveNotificationSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveNotificationSection.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveNotificationSection.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/InteractiveNotificationSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/InteractiveNotificationSection.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/InteractiveNotificationSection.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/FeaturesSection.js"], "sourcesContent": ["import React from \"react\";\r\nimport { FaMicrophoneAlt, FaRegFileAlt, FaMagic, FaRegSmile, FaSyncAlt, FaAccessibleIcon, FaLanguage, FaShieldAlt, FaClock } from \"react-icons/fa\";\r\n\r\nconst features = [\r\n  {\r\n    icon: <FaMicrophoneAlt size={32} className=\"text-indigo-400\" />,\r\n    title: \"Real-Time Transcription\",\r\n    desc: \"Convert speech to text instantly with 99.9% accuracy, powered by advanced neural networks and machine learning.\",\r\n  },\r\n  {\r\n    icon: <FaLanguage size={32} className=\"text-indigo-400\" />,\r\n    title: \"50+ Languages\",\r\n    desc: \"Support for over 50 languages and dialects, with automatic language detection and seamless switching.\",\r\n  },\r\n  {\r\n    icon: <FaMagic size={32} className=\"text-indigo-400\" />,\r\n    title: \"Smart Formatting\",\r\n    desc: \"Automatic punctuation, capitalization, and paragraph breaks. AI understands context for perfect formatting.\",\r\n  },\r\n  {\r\n    icon: <FaRegFileAlt size={32} className=\"text-indigo-400\" />,\r\n    title: \"Multiple Export Formats\",\r\n    desc: \"Export to TXT, DOCX, PDF, or SRT subtitle files. Integrate with Google Docs, Notion, and more.\",\r\n  },\r\n  {\r\n    icon: <FaShieldAlt size={32} className=\"text-indigo-400\" />,\r\n    title: \"Privacy First\",\r\n    desc: \"Your audio is processed securely and never stored. GDPR compliant with end-to-end encryption.\",\r\n  },\r\n  {\r\n    icon: <FaClock size={32} className=\"text-indigo-400\" />,\r\n    title: \"Time Stamps\",\r\n    desc: \"Precise timestamps for every word and sentence, perfect for meeting notes and interview transcripts.\",\r\n  },\r\n  {\r\n    icon: <FaSyncAlt size={32} className=\"text-indigo-400\" />,\r\n    title: \"Live Collaboration\",\r\n    desc: \"Share transcripts in real-time with your team. Multiple users can edit and comment simultaneously.\",\r\n  },\r\n  {\r\n    icon: <FaAccessibleIcon size={32} className=\"text-indigo-400\" />,\r\n    title: \"Accessibility Tools\",\r\n    desc: \"Built-in accessibility features including voice commands, keyboard shortcuts, and screen reader support.\",\r\n  },\r\n  {\r\n    icon: <FaRegSmile size={32} className=\"text-indigo-400\" />,\r\n    title: \"Speaker Recognition\",\r\n    desc: \"Automatically identify and label different speakers in conversations, meetings, and interviews.\",\r\n  },\r\n];\r\n\r\nexport default function FeaturesSection() {\r\n  return (\r\n    <section className=\"w-full flex flex-col items-center justify-center py-24 px-4 bg-transparent\">\r\n      <div className=\"text-center mb-16\">\r\n        <h2 className=\"text-3xl md:text-4xl font-bold text-gray-100 mb-4\">\r\n          Everything you need for <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400\">perfect transcription</span>\r\n        </h2>\r\n        <p className=\"text-gray-400 text-lg max-w-3xl mx-auto\">\r\n          Our AI-powered platform combines cutting-edge technology with intuitive design to deliver the most accurate and efficient voice-to-text experience available.\r\n        </p>\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-7xl\">\r\n        {features.map((feature, idx) => (\r\n          <div\r\n            key={idx}\r\n            className=\"group bg-[#23272a]/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-indigo-500/50 transition-all duration-300 hover:transform hover:scale-105\"\r\n          >\r\n            <div className=\"mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n              {feature.icon}\r\n            </div>\r\n            <h3 className=\"text-xl font-semibold text-gray-100 mb-3\">\r\n              {feature.title}\r\n            </h3>\r\n            <p className=\"text-gray-400 leading-relaxed\">\r\n              {feature.desc}\r\n            </p>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n} \r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,oBAAM,8OAAC,8IAAA,CAAA,kBAAe;YAAC,MAAM;YAAI,WAAU;;;;;;QAC3C,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;;QACtC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,UAAO;YAAC,MAAM;YAAI,WAAU;;;;;;QACnC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,eAAY;YAAC,MAAM;YAAI,WAAU;;;;;;QACxC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,cAAW;YAAC,MAAM;YAAI,WAAU;;;;;;QACvC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,UAAO;YAAC,MAAM;YAAI,WAAU;;;;;;QACnC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,YAAS;YAAC,MAAM;YAAI,WAAU;;;;;;QACrC,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,mBAAgB;YAAC,MAAM;YAAI,WAAU;;;;;;QAC5C,OAAO;QACP,MAAM;IACR;IACA;QACE,oBAAM,8OAAC,8IAAA,CAAA,aAAU;YAAC,MAAM;YAAI,WAAU;;;;;;QACtC,OAAO;QACP,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAoD;0CACxC,8OAAC;gCAAK,WAAU;0CAA+E;;;;;;;;;;;;kCAEzH,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,oBACtB,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI;;;;;;0CAEf,8OAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAEhB,8OAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI;;;;;;;uBAVV;;;;;;;;;;;;;;;;AAiBjB", "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/UseCasesSection.js"], "sourcesContent": ["import React from 'react';\n\nconst useCases = [\n  {\n    title: \"Content Creators\",\n    desc: \"Transform podcasts, videos, and live streams into searchable text content. Perfect for show notes, blog posts, and social media.\",\n    icon: \"🎬\",\n    features: [\"Podcast transcription\", \"Video subtitles\", \"Social media content\"]\n  },\n  {\n    title: \"Business Professionals\",\n    desc: \"Convert meetings, interviews, and calls into actionable notes. Never miss important details or decisions again.\",\n    icon: \"💼\",\n    features: [\"Meeting minutes\", \"Interview notes\", \"Call summaries\"]\n  },\n  {\n    title: \"Students & Researchers\",\n    desc: \"Turn lectures, interviews, and research sessions into organized, searchable text for better study and analysis.\",\n    icon: \"🎓\",\n    features: [\"Lecture notes\", \"Research interviews\", \"Study materials\"]\n  },\n  {\n    title: \"Journalists & Writers\",\n    desc: \"Quickly transcribe interviews, press conferences, and voice memos to focus on crafting compelling stories.\",\n    icon: \"✍️\",\n    features: [\"Interview transcription\", \"Voice memos\", \"Press conferences\"]\n  }\n];\n\nexport default function UseCasesSection() {\n  return (\n    <section className=\"w-full py-24 px-4 bg-gradient-to-b from-[#181d20]/30 to-transparent\">\n      <div className=\"max-w-7xl mx-auto\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-100 mb-4\">\n            Perfect for <span className=\"text-indigo-400\">Every Industry</span>\n          </h2>\n          <p className=\"text-gray-400 text-lg max-w-3xl mx-auto\">\n            From content creation to business meetings, our AI voice-to-text technology adapts to your specific needs and workflow.\n          </p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {useCases.map((useCase, idx) => (\n            <div key={idx} className=\"bg-[#23272a]/80 rounded-2xl p-8 border border-gray-700/50 hover:border-indigo-500/50 transition-all duration-300\">\n              <div className=\"flex items-start gap-4\">\n                <div className=\"text-4xl mb-4\">{useCase.icon}</div>\n                <div className=\"flex-1\">\n                  <h3 className=\"text-2xl font-bold text-gray-100 mb-3\">{useCase.title}</h3>\n                  <p className=\"text-gray-300 mb-6 leading-relaxed\">{useCase.desc}</p>\n                  <div className=\"space-y-2\">\n                    {useCase.features.map((feature, i) => (\n                      <div key={i} className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-indigo-400 rounded-full\"></div>\n                        <span className=\"text-gray-400\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,WAAW;IACf;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;YAAC;YAAyB;YAAmB;SAAuB;IAChF;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;YAAC;YAAmB;YAAmB;SAAiB;IACpE;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;YAAC;YAAiB;YAAuB;SAAkB;IACvE;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;YAAC;YAA2B;YAAe;SAAoB;IAC3E;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CACpD,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAEhD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,oBACtB,8OAAC;4BAAc,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC,QAAQ,KAAK;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAsC,QAAQ,IAAI;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,kBAC9B,8OAAC;wDAAY,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAFzB;;;;;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;;;;;;;;;;;;;AAsBtB", "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/ZigzagAnimatedSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ZigzagAnimatedSection.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ZigzagAnimatedSection.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/ZigzagAnimatedSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ZigzagAnimatedSection.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ZigzagAnimatedSection.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyImageScrollSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StickyImageScrollSection.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StickyImageScrollSection.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/StickyImageScrollSection.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StickyImageScrollSection.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StickyImageScrollSection.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1549, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/MarqueeSection.js"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst logos = [\r\n  {\r\n    name: \"<PERSON>\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"#23272a\"/><ellipse cx=\"20\" cy=\"20\" rx=\"12\" ry=\"8\" fill=\"#b0b6c1\"/></svg>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Epicurious\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"5\" y=\"15\" width=\"30\" height=\"10\" rx=\"5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"25\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#23272a\"/></svg>\r\n    ),\r\n  },\r\n  {\r\n    name: \"GlobalBank\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><polygon points=\"20,5 35,35 5,35\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"25\" r=\"5\" fill=\"#23272a\"/></svg>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Catalog\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><circle cx=\"20\" cy=\"20\" r=\"10\" fill=\"#23272a\"/></svg>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Luminous\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><rect x=\"10\" y=\"10\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"20\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/><rect x=\"10\" y=\"30\" width=\"20\" height=\"5\" rx=\"2.5\" fill=\"#b0b6c1\"/></svg>\r\n    ),\r\n  },\r\n  {\r\n    name: \"Quotient\",\r\n    svg: (\r\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\"><circle cx=\"20\" cy=\"20\" r=\"18\" fill=\"#b0b6c1\"/><rect x=\"18\" y=\"10\" width=\"4\" height=\"20\" rx=\"2\" fill=\"#23272a\"/></svg>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function MarqueeSection() {\r\n  return (\r\n    <section className=\"w-full py-8 bg-gradient-to-b from-[#181d20] to-[#23272a] overflow-hidden\">\r\n      <div className=\"relative w-full\">\r\n        <div className=\"marquee flex items-center gap-16\" style={{animation: 'marquee 24s linear infinite'}}>\r\n          {Array(2).fill(null).map((_, i) => (\r\n            <div className=\"flex items-center gap-16\" key={i}>\r\n              {logos.map((logo, idx) => (\r\n                <div key={logo.name + i} className=\"flex items-center gap-3 min-w-[160px] px-2\">\r\n                  <div className=\"drop-shadow-lg\">{logo.svg}</div>\r\n                  <span className=\"text-gray-200 font-semibold text-lg tracking-wide\" style={{textShadow: '0 2px 8px #181d20'}}> {logo.name} </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      <style>{`\r\n        @keyframes marquee {\r\n          0% { transform: translateX(0); }\r\n          100% { transform: translateX(-50%); }\r\n        }\r\n        .marquee {\r\n          width: 200%;\r\n        }\r\n      `}</style>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAK,MAAK;;;;;;8BAAW,8OAAC;oBAAQ,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,MAAK;;;;;;;;;;;;IAE5J;IACA;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAI,MAAK;;;;;;8BAAW,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAI,IAAG;oBAAM,MAAK;;;;;;;;;;;;IAEzL;IACA;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAQ,QAAO;oBAAkB,MAAK;;;;;;8BAAW,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,MAAK;;;;;;;;;;;;IAErJ;IACA;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAK,MAAK;;;;;;8BAAW,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAK,MAAK;;;;;;;;;;;;IAEnJ;IACA;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAI,IAAG;oBAAM,MAAK;;;;;;8BAAW,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAI,IAAG;oBAAM,MAAK;;;;;;8BAAW,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAI,IAAG;oBAAM,MAAK;;;;;;;;;;;;IAE9P;IACA;QACE,MAAM;QACN,mBACE,8OAAC;YAAI,OAAM;YAAK,QAAO;YAAK,SAAQ;YAAY,MAAK;;8BAAO,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAK,MAAK;;;;;;8BAAW,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAK,OAAM;oBAAI,QAAO;oBAAK,IAAG;oBAAI,MAAK;;;;;;;;;;;;IAErK;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmC,OAAO;wBAAC,WAAW;oBAA6B;8BAC/F,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,kBAC3B,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;oCAAwB,WAAU;;sDACjC,8OAAC;4CAAI,WAAU;sDAAkB,KAAK,GAAG;;;;;;sDACzC,8OAAC;4CAAK,WAAU;4CAAoD,OAAO;gDAAC,YAAY;4CAAmB;;gDAAG;gDAAE,KAAK,IAAI;gDAAC;;;;;;;;mCAFlH,KAAK,IAAI,GAAG;;;;;2BAFqB;;;;;;;;;;;;;;;0BAWrD,8OAAC;0BAAO,CAAC;;;;;;;;MAQT,CAAC;;;;;;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1875, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/components/CTASection.js"], "sourcesContent": ["import React from 'react';\n\nexport default function CTASection() {\n  return (\n    <section className=\"w-full py-24 px-4 bg-gradient-to-b from-transparent to-[#181d20]\">\n      <div className=\"max-w-4xl mx-auto text-center\">\n        <div className=\"bg-[#23272a]/80 rounded-3xl p-12 border border-gray-700/50 relative overflow-hidden\">\n          {/* Background decoration */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10\"></div>\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl\"></div>\n          \n          <div className=\"relative z-10\">\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-100 mb-6\">\n              Ready to Transform Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400\">Voice</span>?\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Join millions of users who trust our AI to convert their speech into perfect text. Start your free trial today.\n            </p>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n              <button className=\"px-10 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-bold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\">\n                Start Free Trial\n              </button>\n              <button className=\"px-10 py-4 border border-gray-600 text-gray-300 font-semibold rounded-full hover:bg-gray-800/50 transition-all duration-300\">\n                Schedule Demo\n              </button>\n            </div>\n            \n            <div className=\"flex items-center justify-center gap-8 text-sm text-gray-400\">\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>No credit card required</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>Cancel anytime</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-green-400\">✓</span>\n                <span>30-day money back</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;kDACxC,8OAAC;wCAAK,WAAU;kDAA+E;;;;;;oCAAY;;;;;;;0CAErI,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA8K;;;;;;kDAGhM,8OAAC;wCAAO,WAAU;kDAA8H;;;;;;;;;;;;0CAKlJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sant<PERSON>h/Whisper%20Flow/my-app/src/app/page.js"], "sourcesContent": ["import Navbar from \"@/components/Navbar\";\nimport Hero from \"@/components/Hero\";\nimport StatsSection from \"@/components/StatsSection\";\nimport StickyAnalyticsSection from \"@/components/StickyAnalyticsSection\";\nimport KeyBenefitsSection from \"@/components/KeyBenefitsSection\";\nimport InteractiveNotificationSection from \"@/components/InteractiveNotificationSection\";\nimport FeaturesSection from \"@/components/FeaturesSection\";\nimport UseCasesSection from \"@/components/UseCasesSection\";\nimport ZigzagAnimatedSection from \"@/components/ZigzagAnimatedSection\";\nimport StickyImageScrollSection from \"@/components/StickyImageScrollSection\";\nimport MarqueeSection from \"@/components/MarqueeSection\";\nimport CTASection from \"@/components/CTASection\";\n\nexport default function Home() {\n  return (\n    <>\n      <Navbar />\n      <Hero />\n      <StatsSection />\n      <StickyAnalyticsSection />\n      <KeyBenefitsSection />\n      <InteractiveNotificationSection />\n      <FeaturesSection />\n      {/* <UseCasesSection /> */}\n      {/* <ZigzagAnimatedSection /> */}\n      {/* <StickyImageScrollSection /> */}\n      <MarqueeSection />\n      {/* <CTASection /> */}\n    </>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,2HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,yHAAA,CAAA,UAAI;;;;;0BACL,8OAAC,iIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,2IAAA,CAAA,UAAsB;;;;;0BACvB,8OAAC,uIAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,mJAAA,CAAA,UAA8B;;;;;0BAC/B,8OAAC,oIAAA,CAAA,UAAe;;;;;0BAIhB,8OAAC,mIAAA,CAAA,UAAc;;;;;;;AAIrB", "debugId": null}}]}