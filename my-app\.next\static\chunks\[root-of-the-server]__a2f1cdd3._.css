/* [next]/internal/font/google/geist_6feb203d.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/973faccb4f6aedb5-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/d26cc22533d232c7-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/b0a57561b6cb5495-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_6feb203d-module__8DQF1a__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_6feb203d-module__8DQF1a__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}

/* [next]/internal/font/google/geist_mono_c7d183a.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/942c7eecbf9bc714-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/e5e2a9f48cda0a81-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/8ee3a1ba4ed5baee-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_c7d183a-module__ZW1U4G__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_c7d183a-module__ZW1U4G__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}

/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --color-red-500: #fb2c36;
    --color-orange-50: #fff7ed;
    --color-orange-100: #ffedd5;
    --color-orange-200: #ffd7a8;
    --color-orange-300: #ffb96d;
    --color-orange-400: #ff8b1a;
    --color-orange-500: #fe6e00;
    --color-yellow-50: #fefce8;
    --color-yellow-200: #fff085;
    --color-yellow-300: #ffe02a;
    --color-yellow-400: #fac800;
    --color-yellow-500: #edb200;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-300: #7bf1a8;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bedbff;
    --color-blue-300: #90c5ff;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-indigo-200: #c7d2ff;
    --color-indigo-300: #a4b3ff;
    --color-indigo-400: #7d87ff;
    --color-indigo-500: #625fff;
    --color-indigo-600: #4f39f6;
    --color-indigo-900: #312c85;
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-400: #c07eff;
    --color-purple-500: #ac4bff;
    --color-purple-600: #9810fa;
    --color-pink-500: #f6339a;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-lg: 0 4px 4px rgba(0, 0, 0, .15);
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-xl: 24px;
    --blur-3xl: 64px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-orange-50: color(display-p3 .99533 .970132 .933499);
      --color-orange-100: color(display-p3 .988762 .931393 .843273);
      --color-orange-200: color(display-p3 .974647 .84705 .680111);
      --color-orange-300: color(display-p3 .96801 .734346 .464159);
      --color-orange-400: color(display-p3 .950192 .561807 .211017);
      --color-orange-500: color(display-p3 .946589 .449788 .0757345);
      --color-yellow-50: color(display-p3 .994197 .988062 .917538);
      --color-yellow-200: color(display-p3 .988789 .943116 .579188);
      --color-yellow-300: color(display-p3 .982669 .880884 .32102);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-green-50: color(display-p3 .950677 .990571 .959366);
      --color-green-100: color(display-p3 .885269 .984329 .910368);
      --color-green-300: color(display-p3 .600292 .935514 .68114);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-blue-50: color(display-p3 .941826 .963151 .995385);
      --color-blue-100: color(display-p3 .869214 .915931 .989622);
      --color-blue-200: color(display-p3 .76688 .855207 .987483);
      --color-blue-300: color(display-p3 .602559 .767214 .993938);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-indigo-200: color(display-p3 .786558 .821755 .988451);
      --color-indigo-300: color(display-p3 .650892 .700156 .990824);
      --color-indigo-400: color(display-p3 .494992 .525291 .985107);
      --color-indigo-500: color(display-p3 .380374 .372235 .971707);
      --color-indigo-600: color(display-p3 .297656 .227891 .929242);
      --color-indigo-900: color(display-p3 .188425 .173312 .503066);
      --color-purple-50: color(display-p3 .977045 .961759 .996715);
      --color-purple-100: color(display-p3 .945034 .910569 .992972);
      --color-purple-400: color(display-p3 .719919 .492497 .995173);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-purple-600: color(display-p3 .546729 .130167 .94439);
      --color-pink-500: color(display-p3 .88894 .276457 .595049);
      --color-gray-50: color(display-p3 .977213 .98084 .985102);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-orange-50: lab(97.7008% 1.53738 5.90646);
      --color-orange-100: lab(94.7127% 3.58391 14.3151);
      --color-orange-200: lab(88.4871% 9.94918 28.8378);
      --color-orange-300: lab(80.8059% 21.7313 50.4455);
      --color-orange-400: lab(70.0429% 42.5156 75.8207);
      --color-orange-500: lab(64.272% 57.1788 90.3583);
      --color-yellow-50: lab(98.6846% -1.79058 9.77662);
      --color-yellow-200: lab(94.3433% -5.00426 52.9663);
      --color-yellow-300: lab(89.7033% -.480324 84.4917);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-green-50: lab(98.1563% -5.60117 2.75913);
      --color-green-100: lab(96.186% -13.8464 6.52362);
      --color-green-300: lab(86.9953% -47.2691 25.0054);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-blue-50: lab(96.492% -1.14647 -5.11479);
      --color-blue-100: lab(92.0301% -2.24757 -11.6453);
      --color-blue-200: lab(86.15% -4.04379 -21.0797);
      --color-blue-300: lab(77.5052% -6.4629 -36.42);
      --color-blue-400: lab(65.0361% -1.42062 -56.9803);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-indigo-200: lab(84.4329% 3.18974 -23.9688);
      --color-indigo-300: lab(74.0235% 8.54138 -41.6075);
      --color-indigo-400: lab(59.866% 22.4833 -64.4485);
      --color-indigo-500: lab(48.295% 38.3129 -81.9673);
      --color-indigo-600: lab(38.4009% 52.6132 -92.3857);
      --color-indigo-900: lab(23.3911% 24.6978 -50.4719);
      --color-purple-50: lab(97.1626% 2.99937 -4.13398);
      --color-purple-100: lab(93.3333% 6.9744 -9.83434);
      --color-purple-400: lab(63.6946% 47.6127 -59.2066);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-purple-600: lab(43.0295% 75.21 -86.5669);
      --color-pink-500: lab(56.9303% 76.8162 -8.07021);
      --color-gray-50: lab(98.2596% -.247031 -.706708);
      --color-gray-100: lab(96.1596% -.082314 -1.13575);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-1\/3 {
    top: 33.3333%;
  }

  .top-8 {
    top: calc(var(--spacing) * 8);
  }

  .top-10 {
    top: calc(var(--spacing) * 10);
  }

  .top-20 {
    top: calc(var(--spacing) * 20);
  }

  .top-32 {
    top: calc(var(--spacing) * 32);
  }

  .-right-2 {
    right: calc(var(--spacing) * -2);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .right-5 {
    right: calc(var(--spacing) * 5);
  }

  .right-10 {
    right: calc(var(--spacing) * 10);
  }

  .-bottom-2 {
    bottom: calc(var(--spacing) * -2);
  }

  .bottom-10 {
    bottom: calc(var(--spacing) * 10);
  }

  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }

  .-left-2 {
    left: calc(var(--spacing) * -2);
  }

  .-left-8 {
    left: calc(var(--spacing) * -8);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-5 {
    left: calc(var(--spacing) * 5);
  }

  .left-10 {
    left: calc(var(--spacing) * 10);
  }

  .z-0 {
    z-index: 0;
  }

  .z-10 {
    z-index: 10;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .row-span-1 {
    grid-row: span 1 / span 1;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-1 {
    margin: calc(var(--spacing) * 1);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-14 {
    margin-bottom: calc(var(--spacing) * 14);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-18 {
    height: calc(var(--spacing) * 18);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-80 {
    height: calc(var(--spacing) * 80);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[420px\] {
    height: 420px;
  }

  .h-full {
    height: 100%;
  }

  .min-h-\[80vh\] {
    min-height: 80vh;
  }

  .min-h-\[260px\] {
    min-height: 260px;
  }

  .min-h-\[600px\] {
    min-height: 600px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-30 {
    width: calc(var(--spacing) * 30);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[160px\] {
    min-width: 160px;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .-rotate-6 {
    rotate: -6deg;
  }

  .rotate-3 {
    rotate: 3deg;
  }

  .rotate-6 {
    rotate: 6deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .list-disc {
    list-style-type: disc;
  }

  .auto-rows-\[minmax\(120px\,auto\)\] {
    grid-auto-rows: minmax(120px, auto);
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-rows-6 {
    grid-template-rows: repeat(6, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
  }

  .rounded-t-full {
    border-top-left-radius: 3.40282e38px;
    border-top-right-radius: 3.40282e38px;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-500 {
    border-color: var(--color-gray-500);
  }

  .border-gray-600 {
    border-color: var(--color-gray-600);
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-gray-700\/50 {
    border-color: rgba(54, 65, 83, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-700\/50 {
      border-color: color-mix(in oklab, var(--color-gray-700) 50%, transparent);
    }
  }

  .border-gray-700\/60 {
    border-color: rgba(54, 65, 83, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-700\/60 {
      border-color: color-mix(in oklab, var(--color-gray-700) 60%, transparent);
    }
  }

  .border-gray-800 {
    border-color: var(--color-gray-800);
  }

  .border-indigo-500\/30 {
    border-color: rgba(98, 95, 255, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-indigo-500\/30 {
      border-color: color-mix(in oklab, var(--color-indigo-500) 30%, transparent);
    }
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .bg-\[\#181d20\] {
    background-color: #181d20;
  }

  .bg-\[\#181d20\]\/80 {
    background-color: rgba(24, 29, 32, .8);
    background-color: color(display-p3 .0978589 .113125 .124198 / .8);
    background-color: lab(10.3461% -1.75506 -2.89351 / .8);
  }

  .bg-\[\#23272a\] {
    background-color: #23272a;
  }

  .bg-\[\#23272a\]\/60 {
    background-color: rgba(35, 39, 42, .6);
    background-color: color(display-p3 .14017 .152447 .163452 / .6);
    background-color: lab(15.3225% -1.26514 -2.63232 / .6);
  }

  .bg-\[\#23272a\]\/80 {
    background-color: rgba(35, 39, 42, .8);
    background-color: color(display-p3 .14017 .152447 .163452 / .8);
    background-color: lab(15.3225% -1.26514 -2.63232 / .8);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }

  .bg-blue-300 {
    background-color: var(--color-blue-300);
  }

  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }

  .bg-gray-800\/50 {
    background-color: rgba(30, 41, 57, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-800\/50 {
      background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
    }
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-300 {
    background-color: var(--color-green-300);
  }

  .bg-green-400 {
    background-color: var(--color-green-400);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-indigo-200 {
    background-color: var(--color-indigo-200);
  }

  .bg-indigo-300 {
    background-color: var(--color-indigo-300);
  }

  .bg-indigo-400 {
    background-color: var(--color-indigo-400);
  }

  .bg-indigo-500 {
    background-color: var(--color-indigo-500);
  }

  .bg-indigo-500\/5 {
    background-color: rgba(98, 95, 255, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-indigo-500\/5 {
      background-color: color-mix(in oklab, var(--color-indigo-500) 5%, transparent);
    }
  }

  .bg-indigo-500\/10 {
    background-color: rgba(98, 95, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-indigo-500\/10 {
      background-color: color-mix(in oklab, var(--color-indigo-500) 10%, transparent);
    }
  }

  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }

  .bg-indigo-600\/20 {
    background-color: rgba(79, 57, 246, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-indigo-600\/20 {
      background-color: color-mix(in oklab, var(--color-indigo-600) 20%, transparent);
    }
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-200 {
    background-color: var(--color-orange-200);
  }

  .bg-orange-300 {
    background-color: var(--color-orange-300);
  }

  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }

  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-500\/10 {
    background-color: rgba(172, 75, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-purple-500\/10 {
      background-color: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }

  .bg-yellow-300 {
    background-color: var(--color-yellow-300);
  }

  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-\[\#181d20\] {
    --tw-gradient-from: #181d20;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#181d20\]\/30 {
    --tw-gradient-from: rgba(24, 29, 32, .3);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .from-\[\#181d20\]\/30 {
      --tw-gradient-from: color(display-p3 .0978589 .113125 .124198 / .3);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .from-\[\#181d20\]\/30 {
      --tw-gradient-from: lab(10.3461% -1.75506 -2.89351 / .3);
    }
  }

  .from-\[\#23272a\] {
    --tw-gradient-from: #23272a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-200 {
    --tw-gradient-from: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-400 {
    --tw-gradient-from: var(--color-indigo-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-600 {
    --tw-gradient-from: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-600\/10 {
    --tw-gradient-from: rgba(79, 57, 246, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-indigo-600\/10 {
      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-600) 10%, transparent);
    }
  }

  .from-indigo-600\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-900 {
    --tw-gradient-from: var(--color-indigo-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-50 {
    --tw-gradient-from: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-300 {
    --tw-gradient-from: var(--color-orange-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-400 {
    --tw-gradient-from: var(--color-orange-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-purple-500 {
    --tw-gradient-via: var(--color-purple-500);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-\[\#181d20\] {
    --tw-gradient-to: #181d20;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#181d20\]\/50 {
    --tw-gradient-to: rgba(24, 29, 32, .5);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color(display-p3 0 0 0)) {
    .to-\[\#181d20\]\/50 {
      --tw-gradient-to: color(display-p3 .0978589 .113125 .124198 / .5);
    }
  }

  @supports (color: lab(0% 0 0)) {
    .to-\[\#181d20\]\/50 {
      --tw-gradient-to: lab(10.3461% -1.75506 -2.89351 / .5);
    }
  }

  .to-\[\#23272a\] {
    --tw-gradient-to: #23272a;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-400 {
    --tw-gradient-to: var(--color-gray-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-400 {
    --tw-gradient-to: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600\/10 {
    --tw-gradient-to: rgba(152, 16, 250, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-purple-600\/10 {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-600) 10%, transparent);
    }
  }

  .to-purple-600\/10 {
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-yellow-50 {
    --tw-gradient-to: var(--color-yellow-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-yellow-400 {
    --tw-gradient-to: var(--color-yellow-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-yellow-500 {
    --tw-gradient-to: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-10 {
    padding: calc(var(--spacing) * 10);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-gray-100 {
    color: var(--color-gray-100);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-indigo-200 {
    color: var(--color-indigo-200);
  }

  .text-indigo-300 {
    color: var(--color-indigo-300);
  }

  .text-indigo-400 {
    color: var(--color-indigo-400);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-transparent {
    color: rgba(0, 0, 0, 0);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/90 {
    color: rgba(255, 255, 255, .9);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .uppercase {
    text-transform: uppercase;
  }

  .underline {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-40 {
    opacity: .4;
  }

  .opacity-50 {
    opacity: .5;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-indigo-500\/40 {
    --tw-ring-color: rgba(98, 95, 255, .4);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-indigo-500\/40 {
      --tw-ring-color: color-mix(in oklab, var(--color-indigo-500) 40%, transparent);
    }
  }

  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .blur-xl {
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgba(0, 0, 0, .15)));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .delay-100 {
    transition-delay: .1s;
  }

  .delay-200 {
    transition-delay: .2s;
  }

  .delay-500 {
    transition-delay: .5s;
  }

  .delay-1000 {
    transition-delay: 1s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-700 {
    --tw-duration: .7s;
    transition-duration: .7s;
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-110:is(:where(.group):hover *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:transform:is(:where(.group):hover *) {
      transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:rotate-0:hover {
      rotate: none;
    }
  }

  @media (hover: hover) {
    .hover\:transform:hover {
      transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
    }
  }

  @media (hover: hover) {
    .hover\:border-indigo-500\/50:hover {
      border-color: rgba(98, 95, 255, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-indigo-500\/50:hover {
        border-color: color-mix(in oklab, var(--color-indigo-500) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-orange-400:hover {
      border-color: var(--color-orange-400);
    }
  }

  @media (hover: hover) {
    .hover\:border-orange-400\/50:hover {
      border-color: rgba(255, 139, 26, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-orange-400\/50:hover {
        border-color: color-mix(in oklab, var(--color-orange-400) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#23272a\]:hover {
      background-color: #23272a;
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800\/50:hover {
      background-color: rgba(30, 41, 57, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-800\/50:hover {
        background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-indigo-500:hover {
      background-color: var(--color-indigo-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-orange-500:hover {
      background-color: var(--color-orange-500);
    }
  }

  @media (hover: hover) {
    .hover\:from-gray-300:hover {
      --tw-gradient-from: var(--color-gray-300);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-gray-500:hover {
      --tw-gradient-to: var(--color-gray-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-orange-400:hover {
      color: var(--color-orange-400);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-rows-3 {
      grid-template-rows: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:px-16 {
      padding-inline: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 48rem) {
    .md\:py-0 {
      padding-block: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-7xl {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 64rem) {
    .lg\:top-32 {
      top: calc(var(--spacing) * 32);
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-fit {
      height: -moz-fit-content;
      height: fit-content;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__a2f1cdd3._.css.map*/