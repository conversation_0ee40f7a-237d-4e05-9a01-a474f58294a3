@import "tailwindcss";

:root {
  /* Kambaa AI Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors - Light Blue */
  --secondary-50: #f0f9ff;
  --secondary-100: #e0f2fe;
  --secondary-200: #bae6fd;
  --secondary-300: #7dd3fc;
  --secondary-400: #38bdf8;
  --secondary-500: #0ea5e9;
  --secondary-600: #0284c7;
  --secondary-700: #0369a1;
  --secondary-800: #075985;
  --secondary-900: #0c4a6e;

  /* Neutral Colors */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  /* Typography Scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--neutral-100);
}

/* Consistent heading styles */
h1 {
  font-size: var(--text-5xl);
  font-weight: 700;
  line-height: 1.2;
  color: var(--neutral-100);
}

h2 {
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: 1.3;
  color: var(--neutral-100);
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--neutral-100);
}

h4 {
  font-size: var(--text-xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--neutral-200);
}

p {
  font-size: var(--text-lg);
  line-height: 1.6;
  color: var(--neutral-300);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white !important;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--neutral-300) !important;
  font-weight: 600;
  padding: 1rem 2rem;
  border: 2px solid var(--neutral-600);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-secondary:hover {
  border-color: var(--primary-500);
  color: var(--primary-400) !important;
  background: rgba(99, 102, 241, 0.1);
}

/* Improved text contrast and visibility */
.text-contrast-high {
  color: var(--neutral-100) !important;
}

.text-contrast-medium {
  color: var(--neutral-300) !important;
}

.text-contrast-low {
  color: var(--neutral-500) !important;
}

/* Ensure proper text visibility on different backgrounds */
.bg-dark {
  background-color: var(--neutral-900);
}

.bg-dark * {
  color: var(--neutral-100);
}

.bg-light {
  background-color: var(--neutral-50);
}

.bg-light * {
  color: var(--neutral-900);
}
