"use client"
import React, { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);

const analyticsFeatures = [
  {
    id: 1,
    title: "Reach the Right Audiences",
    description: "Connect with the people who matter most. Our platform helps you target and engage the right audiences with precision.",
    icon: "👥",
    bgColor: "bg-blue-50",
    iconBg: "bg-blue-100"
  },
  {
    id: 2,
    title: "Real-Time Performance Tracking",
    description: "Monitor your campaigns and content performance in real-time. Get instant insights and make data-driven decisions faster.",
    icon: "📊",
    bgColor: "bg-green-50",
    iconBg: "bg-green-100"
  },
  {
    id: 3,
    title: "Advanced Analytics Dashboard",
    description: "Comprehensive analytics dashboard with detailed metrics, custom reports, and actionable insights for better ROI.",
    icon: "📈",
    bgColor: "bg-purple-50",
    iconBg: "bg-purple-100"
  },
  {
    id: 4,
    title: "Multi-Platform Integration",
    description: "Seamlessly integrate with all major social media platforms and marketing tools. Centralize your analytics in one place.",
    icon: "🔗",
    bgColor: "bg-orange-50",
    iconBg: "bg-orange-100"
  }
];

export default function StickyAnalyticsSection() {
  const [activeIdx, setActiveIdx] = useState(0);
  const contentRefs = useRef([]);
  const sectionRef = useRef();

  useEffect(() => {
    contentRefs.current.forEach((el, idx) => {
      if (!el) return;
      
      ScrollTrigger.create({
        trigger: el,
        start: "top center",
        end: "bottom center",
        onEnter: () => setActiveIdx(idx),
        onEnterBack: () => setActiveIdx(idx),
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="w-full py-24 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Left side - Sticky content */}
          <div className="lg:sticky lg:top-32 lg:h-fit">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              We make it easy to track all analytics
            </h2>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              We make tracking your analytics simple and stress-free. Get clear, real-time insights in one place and make smarter decisions without the complexity.
            </p>
            <button className="px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300">
              Get Started Now
            </button>
          </div>
          
          {/* Right side - Scrollable feature boxes */}
          <div className="space-y-8">
            {analyticsFeatures.map((feature, idx) => (
              <div
                key={feature.id}
                ref={el => (contentRefs.current[idx] = el)}
                className={`${feature.bgColor} rounded-2xl p-8 border-2 transition-all duration-500 ${
                  activeIdx === idx ? 'border-gray-300 shadow-lg scale-105' : 'border-transparent'
                }`}
                style={{ minHeight: '300px' }}
              >
                <div className="flex items-start gap-6">
                  <div className={`${feature.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center text-2xl flex-shrink-0`}>
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
                
                {/* Visual representation based on feature */}
                <div className="mt-8">
                  {idx === 0 && (
                    <div className="flex items-center justify-center">
                      <div className="relative">
                        <div className="w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-lg">
                          <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-2xl">👤</span>
                          </div>
                        </div>
                        <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full"></div>
                        <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-blue-300 rounded-full"></div>
                        <div className="absolute top-8 -left-8 w-4 h-4 bg-blue-200 rounded-full"></div>
                      </div>
                    </div>
                  )}
                  
                  {idx === 1 && (
                    <div className="bg-white rounded-lg p-4">
                      <div className="flex items-end justify-between h-20">
                        <div className="w-8 bg-green-300 rounded-t" style={{height: '40%'}}></div>
                        <div className="w-8 bg-green-400 rounded-t" style={{height: '60%'}}></div>
                        <div className="w-8 bg-green-500 rounded-t" style={{height: '80%'}}></div>
                        <div className="w-8 bg-green-600 rounded-t" style={{height: '100%'}}></div>
                        <div className="w-8 bg-green-400 rounded-t" style={{height: '70%'}}></div>
                      </div>
                    </div>
                  )}
                  
                  {idx === 2 && (
                    <div className="bg-white rounded-lg p-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">2.4K</div>
                          <div className="text-sm text-gray-600">Views</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">89%</div>
                          <div className="text-sm text-gray-600">Engagement</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">156</div>
                          <div className="text-sm text-gray-600">Conversions</div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {idx === 3 && (
                    <div className="flex justify-center gap-4">
                      <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold">f</div>
                      <div className="w-12 h-12 bg-pink-500 rounded-lg flex items-center justify-center text-white font-bold">ig</div>
                      <div className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white font-bold">tw</div>
                      <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold">yt</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
