"use client"
import React, { useRef, useEffect, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);

const analyticsFeatures = [
  {
    id: 1,
    title: "Real-Time Speech Recognition",
    description: "Experience instant voice-to-text conversion with our advanced AI. Perfect for live meetings, interviews, and lectures with zero delay.",
    icon: "🎤",
    bgColor: "bg-blue-50",
    iconBg: "bg-blue-100"
  },
  {
    id: 2,
    title: "Multi-Language Support",
    description: "Break language barriers with support for 50+ languages and dialects. Our AI adapts to accents and speaking styles automatically.",
    icon: "🌍",
    bgColor: "bg-green-50",
    iconBg: "bg-green-100"
  },
  {
    id: 3,
    title: "Smart Audio Enhancement",
    description: "Advanced noise reduction and audio clarity enhancement ensure perfect transcription even in challenging environments.",
    icon: "🔊",
    bgColor: "bg-purple-50",
    iconBg: "bg-purple-100"
  },
  {
    id: 4,
    title: "Export & Integration",
    description: "Export transcripts in multiple formats (TXT, DOCX, SRT, VTT) and integrate seamlessly with your favorite productivity tools.",
    icon: "📄",
    bgColor: "bg-orange-50",
    iconBg: "bg-orange-100"
  }
];

export default function StickyAnalyticsSection() {
  const [activeIdx, setActiveIdx] = useState(0);
  const contentRefs = useRef([]);
  const sectionRef = useRef();

  useEffect(() => {
    contentRefs.current.forEach((el, idx) => {
      if (!el) return;
      
      ScrollTrigger.create({
        trigger: el,
        start: "top center",
        end: "bottom center",
        onEnter: () => setActiveIdx(idx),
        onEnterBack: () => setActiveIdx(idx),
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="w-full py-24 px-4" style={{backgroundColor: 'var(--neutral-50)'}}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Left side - Sticky content */}
          <div className="lg:sticky lg:top-32 lg:h-fit">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight" style={{color: 'var(--neutral-900)'}}>
              We make audio transcription effortless
            </h2>
            <p className="text-lg md:text-xl mb-8 leading-relaxed" style={{color: 'var(--neutral-600)'}}>
              Transform your audio into accurate text with our AI-powered transcription service. Simple, fast, and reliable - perfect for professionals, students, and content creators.
            </p>
            <button className="btn-primary text-lg px-8 py-4">
              Start Transcribing
            </button>
          </div>
          
          {/* Right side - Scrollable feature boxes */}
          <div className="space-y-8">
            {analyticsFeatures.map((feature, idx) => (
              <div
                key={feature.id}
                ref={el => (contentRefs.current[idx] = el)}
                className={`${feature.bgColor} rounded-2xl p-8 border-2 transition-all duration-500 ${
                  activeIdx === idx ? 'border-gray-300 shadow-lg scale-105' : 'border-transparent'
                }`}
                style={{ minHeight: '300px' }}
              >
                <div className="flex items-start gap-6">
                  <div className={`${feature.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center text-2xl flex-shrink-0`}>
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <h3 style={{fontSize: 'var(--text-2xl)', color: 'var(--neutral-900)'}} className="font-bold mb-4">
                      {feature.title}
                    </h3>
                    <p style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-700)'}} className="leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
                
                {/* Visual representation based on feature */}
                <div className="mt-8">
                  {idx === 0 && (
                    <div className="flex items-center justify-center">
                      <div className="relative">
                        <div className="w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-lg">
                          <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-2xl">🎤</span>
                          </div>
                        </div>
                        <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
                        <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-blue-300 rounded-full animate-pulse delay-100"></div>
                        <div className="absolute top-8 -left-8 w-4 h-4 bg-blue-200 rounded-full animate-pulse delay-200"></div>
                      </div>
                    </div>
                  )}

                  {idx === 1 && (
                    <div className="bg-white rounded-lg p-4">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <span className="text-sm text-gray-600">🇺🇸 🇪🇸 🇫🇷 🇩🇪 🇯🇵</span>
                      </div>
                      <div className="flex items-end justify-between h-16">
                        <div className="w-6 bg-green-300 rounded-t" style={{height: '40%'}}></div>
                        <div className="w-6 bg-green-400 rounded-t" style={{height: '60%'}}></div>
                        <div className="w-6 bg-green-500 rounded-t" style={{height: '80%'}}></div>
                        <div className="w-6 bg-green-600 rounded-t" style={{height: '100%'}}></div>
                        <div className="w-6 bg-green-400 rounded-t" style={{height: '70%'}}></div>
                        <div className="w-6 bg-green-500 rounded-t" style={{height: '85%'}}></div>
                      </div>
                    </div>
                  )}

                  {idx === 2 && (
                    <div className="bg-white rounded-lg p-4">
                      <div className="flex items-center justify-center mb-4">
                        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-2xl">🔊</span>
                        </div>
                      </div>
                      <div className="flex items-end justify-center gap-1 h-12">
                        <div className="w-2 bg-purple-300 rounded-t" style={{height: '30%'}}></div>
                        <div className="w-2 bg-purple-500 rounded-t" style={{height: '70%'}}></div>
                        <div className="w-2 bg-purple-600 rounded-t" style={{height: '100%'}}></div>
                        <div className="w-2 bg-purple-400 rounded-t" style={{height: '50%'}}></div>
                        <div className="w-2 bg-purple-500 rounded-t" style={{height: '80%'}}></div>
                      </div>
                    </div>
                  )}

                  {idx === 3 && (
                    <div className="flex justify-center gap-3">
                      <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center text-white text-xs font-bold">TXT</div>
                      <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs font-bold">DOC</div>
                      <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white text-xs font-bold">SRT</div>
                      <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs font-bold">VTT</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
