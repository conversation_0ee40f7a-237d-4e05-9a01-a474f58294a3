import React from 'react';

const stats = [
  { number: "99.9%", label: "Accuracy Rate", desc: "Industry-leading precision" },
  { number: "50+", label: "Languages", desc: "Global language support" },
  { number: "10M+", label: "Words Processed", desc: "Daily transcriptions" },
  { number: "<1s", label: "Response Time", desc: "Lightning-fast processing" }
];

export default function StatsSection() {
  return (
    <section className="w-full py-20 px-4 bg-gradient-to-b from-transparent to-[#181d20]/50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-100 mb-4">
            Trusted by <span className="text-indigo-400">Millions</span> Worldwide
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Our AI technology powers conversations across industries, from podcasting to professional meetings.
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, idx) => (
            <div key={idx} className="text-center group">
              <div className="bg-[#23272a]/80 rounded-2xl p-8 border border-gray-700/50 hover:border-indigo-500/50 transition-all duration-300 group-hover:transform group-hover:scale-105">
                <div className="text-4xl md:text-5xl font-bold text-indigo-400 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-200 font-semibold mb-1">
                  {stat.label}
                </div>
                <div className="text-gray-400 text-sm">
                  {stat.desc}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}