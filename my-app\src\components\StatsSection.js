import React from 'react';

const stats = [
  { number: "$3B+", label: "processed Sales" },
  { number: "50k+", label: "Product sold" },
  { number: "20+", label: "Transaction per day" }
];

export default function StatsSection() {
  return (
    <section className="w-full py-20 px-4 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-600">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 items-center">
          {/* Left side - Text content */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl md:text-3xl font-bold text-white leading-tight">
              Over 12k companies trust Retrov for planning.
            </h2>
          </div>

          {/* Right side - Stats */}
          <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, idx) => (
              <div key={idx} className="text-center lg:text-left">
                <div className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-white/90 text-lg font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}