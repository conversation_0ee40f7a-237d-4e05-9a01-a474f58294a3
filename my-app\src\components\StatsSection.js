import React from 'react';

const stats = [
  { number: "10M+", label: "Hours Transcribed" },
  { number: "99.9%", label: "Accuracy Rate" },
  { number: "50+", label: "Languages Supported" }
];

export default function StatsSection() {
  return (
    <section className="w-full py-20 px-4" style={{background: 'linear-gradient(135deg, var(--primary-600), var(--secondary-600))'}}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 items-center">
          {/* Left side - Text content */}
          <div className="lg:col-span-1">
            <h2 style={{fontSize: 'var(--text-3xl)', color: 'white'}} className="font-bold leading-tight">
              Over 100k+ users trust ZINLE AI for transcription.
            </h2>
          </div>

          {/* Right side - Stats */}
          <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, idx) => (
              <div key={idx} className="text-center lg:text-left">
                <div style={{fontSize: 'var(--text-5xl)'}} className="font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div style={{fontSize: 'var(--text-lg)'}} className="text-white/90 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}