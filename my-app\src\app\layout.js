import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen`}
        style={{
          background:
            "radial-gradient(ellipse at 30% 20%, var(--neutral-800) 60%, var(--neutral-900) 100%)",
          position: 'relative',
        }}
      >
        {/* Black vignette overlay for side shading */}
        <div
          style={{
            pointerEvents: 'none',
            position: 'fixed',
            inset: 0,
            zIndex: 0,
            background:
              'linear-gradient(90deg, #000 0%, transparent 20%, transparent 80%, #000 100%)',
            opacity: 0.7,
          }}
          aria-hidden="true"
        />
        <div style={{ position: 'relative', zIndex: 1 }}>{children}</div>
      </body>
    </html>
  );
}
