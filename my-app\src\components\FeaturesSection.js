import React from "react";
import { FaMicrophoneAlt, FaRegFileAlt, FaMagic, FaRegSmile, FaSyncAlt, FaAccessibleIcon, FaLanguage, FaShieldAlt, FaClock } from "react-icons/fa";

const features = [
  {
    icon: <FaMicrophoneAlt size={32} className="text-indigo-400" />,
    title: "Real-Time Transcription",
    desc: "Convert speech to text instantly with 99.9% accuracy, powered by advanced neural networks and machine learning.",
  },
  {
    icon: <FaLanguage size={32} className="text-indigo-400" />,
    title: "50+ Languages",
    desc: "Support for over 50 languages and dialects, with automatic language detection and seamless switching.",
  },
  {
    icon: <FaMagic size={32} className="text-indigo-400" />,
    title: "Smart Formatting",
    desc: "Automatic punctuation, capitalization, and paragraph breaks. AI understands context for perfect formatting.",
  },
  {
    icon: <FaRegFileAlt size={32} className="text-indigo-400" />,
    title: "Multiple Export Formats",
    desc: "Export to TXT, DOCX, PDF, or SRT subtitle files. Integrate with Google Docs, Notion, and more.",
  },
  {
    icon: <FaShieldAlt size={32} className="text-indigo-400" />,
    title: "Privacy First",
    desc: "Your audio is processed securely and never stored. GDPR compliant with end-to-end encryption.",
  },
  {
    icon: <FaClock size={32} className="text-indigo-400" />,
    title: "Time Stamps",
    desc: "Precise timestamps for every word and sentence, perfect for meeting notes and interview transcripts.",
  },
  {
    icon: <FaSyncAlt size={32} className="text-indigo-400" />,
    title: "Live Collaboration",
    desc: "Share transcripts in real-time with your team. Multiple users can edit and comment simultaneously.",
  },
  {
    icon: <FaAccessibleIcon size={32} className="text-indigo-400" />,
    title: "Accessibility Tools",
    desc: "Built-in accessibility features including voice commands, keyboard shortcuts, and screen reader support.",
  },
  {
    icon: <FaRegSmile size={32} className="text-indigo-400" />,
    title: "Speaker Recognition",
    desc: "Automatically identify and label different speakers in conversations, meetings, and interviews.",
  },
];

export default function FeaturesSection() {
  return (
    <section className="w-full flex flex-col items-center justify-center py-24 px-4 bg-transparent">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-100 mb-4">
          Everything you need for <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">perfect transcription</span>
        </h2>
        <p className="text-gray-400 text-lg max-w-3xl mx-auto">
          Our AI-powered platform combines cutting-edge technology with intuitive design to deliver the most accurate and efficient voice-to-text experience available.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-7xl">
        {features.map((feature, idx) => (
          <div
            key={idx}
            className="group bg-[#23272a]/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-indigo-500/50 transition-all duration-300 hover:transform hover:scale-105"
          >
            <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
              {feature.icon}
            </div>
            <h3 className="text-xl font-semibold text-gray-100 mb-3">
              {feature.title}
            </h3>
            <p className="text-gray-400 leading-relaxed">
              {feature.desc}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
} 
