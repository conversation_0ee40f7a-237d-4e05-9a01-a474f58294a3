import React from 'react';
import window from '../../public/windows.png';
import mac from "../../public/mac.png";
import Image from 'next/image';

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center px-4 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 rounded-full blur-xl animate-pulse" style={{backgroundColor: 'var(--primary-500)', opacity: 0.1}}></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 rounded-full blur-xl animate-pulse delay-1000" style={{backgroundColor: 'var(--secondary-500)', opacity: 0.1}}></div>
      </div>

      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative z-10 pt-20">
        {/* Left side - Content */}
        <div className="space-y-8">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6" style={{color: 'var(--neutral-100)'}}>
            Transform Your Voice Into Perfect Text with{' '}
            <span style={{color: 'var(--primary-400)'}}>AI</span>
          </h1>

          <p className="text-lg md:text-xl leading-relaxed max-w-lg mb-8" style={{color: 'var(--neutral-300)'}}>
            Experience lightning-fast, AI-powered speech-to-text conversion with 99.9% accuracy. Perfect for meetings, interviews, podcasts, and content creation.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <button className="btn-primary flex  items-center gap-2">
              <Image src={window} width={20} height={20}/>
              Download For Windows
            </button>
            <button className="btn-secondary flex  items-center gap-2">
              <Image src={mac} width={20} height={20}/>
              Download For Mac
            </button>
          </div>
        </div>

        {/* Right side - Audio Transcription Dashboard */}
        <div className="relative">
          <div className="bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500">
            {/* Dashboard header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg flex items-center justify-center" style={{backgroundColor: 'var(--primary-600)'}}>
                  <span className="text-white text-sm">🎤</span>
                </div>
                <span className="font-semibold" style={{color: 'var(--neutral-800)'}}>Kambaa AI</span>
              </div>
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
            </div>

            {/* Dashboard content */}
            <div className="space-y-4">
              {/* Stats cards */}
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">99.9%</div>
                  <div className="text-sm text-gray-600">Accuracy</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">2.4s</div>
                  <div className="text-sm text-gray-600">Avg Speed</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">50+</div>
                  <div className="text-sm text-gray-600">Languages</div>
                </div>
              </div>

              {/* Waveform visualization */}
              <div className="bg-gray-50 rounded-lg p-4 h-32">
                <div className="flex items-end justify-between h-full gap-1">
                  <div className="w-2 bg-indigo-300 rounded-t" style={{height: '40%'}}></div>
                  <div className="w-2 bg-indigo-500 rounded-t" style={{height: '70%'}}></div>
                  <div className="w-2 bg-indigo-600 rounded-t" style={{height: '100%'}}></div>
                  <div className="w-2 bg-indigo-400 rounded-t" style={{height: '60%'}}></div>
                  <div className="w-2 bg-indigo-500 rounded-t" style={{height: '85%'}}></div>
                  <div className="w-2 bg-indigo-300 rounded-t" style={{height: '45%'}}></div>
                  <div className="w-2 bg-indigo-600 rounded-t" style={{height: '90%'}}></div>
                  <div className="w-2 bg-indigo-400 rounded-t" style={{height: '55%'}}></div>
                  <div className="w-2 bg-indigo-500 rounded-t" style={{height: '75%'}}></div>
                  <div className="w-2 bg-indigo-300 rounded-t" style={{height: '35%'}}></div>
                </div>
              </div>

              {/* Transcription preview */}
              <div className="flex gap-4">
                <div className="w-1/3 space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-indigo-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="flex-1">
                  <div className="h-16 bg-gray-100 rounded-lg flex items-center px-3">
                    <span className="text-xs text-gray-600">"Hello, this is a live transcription..."</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute -top-4 -right-4 w-16 h-16 bg-indigo-400 rounded-full flex items-center justify-center animate-bounce">
            <span className="text-2xl">🎵</span>
          </div>
        </div>
      </div>
    </section>
  );
}
