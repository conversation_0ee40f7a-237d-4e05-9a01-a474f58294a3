import React from 'react';

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-indigo-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-6xl mx-auto text-center relative z-10">
        <div className="mb-8">
          <span className="inline-block px-4 py-2 bg-indigo-600/20 text-indigo-300 rounded-full text-sm font-medium border border-indigo-500/30">
            🎤 AI-Powered Voice Recognition
          </span>
        </div>
        
        <h1 className="text-5xl md:text-7xl font-bold text-gray-100 mb-6 leading-tight">
          Transform Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">Voice</span> Into Perfect Text
        </h1>
        
        <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
          Experience lightning-fast, AI-powered speech-to-text conversion with 99.9% accuracy. Perfect for content creators, professionals, and anyone who thinks faster than they type.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <button className="px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            Start Converting Now - Free
          </button>
          <button className="px-8 py-4 border border-gray-600 text-gray-300 font-semibold rounded-full hover:bg-gray-800/50 transition-all duration-300">
            Watch Demo
          </button>
        </div>
        
        {/* Live demo preview */}
        <div className="bg-[#181d20]/80 rounded-2xl p-8 max-w-4xl mx-auto backdrop-blur-sm border border-gray-700/50">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-gray-400 text-sm ml-2">Live Transcription</span>
          </div>
          <div className="text-left">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">🎤</span>
              </div>
              <span className="text-gray-400">Speaking...</span>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce delay-200"></div>
              </div>
            </div>
            <p className="text-gray-200 text-lg leading-relaxed">
              "Hello, this is a demonstration of our AI voice-to-text technology. As you can see, it converts speech to text in real-time with incredible accuracy and proper formatting."
            </p>
          </div>
        </div>
      </div>
    </section>
  );
} 
