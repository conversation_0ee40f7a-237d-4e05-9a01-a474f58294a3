import React from 'react';

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center px-4 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-indigo-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      </div>

      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative z-10">
        {/* Left side - Content */}
        <div className="space-y-8">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-100 leading-tight">
            Get the most out of your social media analytics
          </h1>

          <p className="text-lg md:text-xl text-gray-300 leading-relaxed max-w-lg">
            Gain deeper insights, track performance, and make data-driven decisions across every platform.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <button className="px-8 py-4 bg-black text-white font-semibold rounded-lg hover:bg-gray-800 transition-all duration-300">
              Request a demo
            </button>
            <button className="px-8 py-4 text-gray-300 font-semibold hover:text-white transition-all duration-300 underline">
              How it works?
            </button>
          </div>
        </div>

        {/* Right side - Dashboard mockup */}
        <div className="relative">
          <div className="bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500">
            {/* Dashboard header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">R</span>
                </div>
                <span className="font-semibold text-gray-800">Retroverse</span>
              </div>
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
            </div>

            {/* Dashboard content */}
            <div className="space-y-4">
              {/* Stats cards */}
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">7,265</div>
                  <div className="text-sm text-gray-600">Total Views</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">+671</div>
                  <div className="text-sm text-gray-600">New Users</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-800">156</div>
                  <div className="text-sm text-gray-600">Conversions</div>
                </div>
              </div>

              {/* Chart area */}
              <div className="bg-gray-50 rounded-lg p-4 h-32">
                <div className="flex items-end justify-between h-full">
                  <div className="w-8 bg-indigo-200 rounded-t" style={{height: '60%'}}></div>
                  <div className="w-8 bg-indigo-400 rounded-t" style={{height: '80%'}}></div>
                  <div className="w-8 bg-indigo-600 rounded-t" style={{height: '100%'}}></div>
                  <div className="w-8 bg-indigo-400 rounded-t" style={{height: '70%'}}></div>
                  <div className="w-8 bg-indigo-300 rounded-t" style={{height: '90%'}}></div>
                  <div className="w-8 bg-indigo-500 rounded-t" style={{height: '65%'}}></div>
                </div>
              </div>

              {/* Navigation sidebar mockup */}
              <div className="flex gap-4">
                <div className="w-1/3 space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-indigo-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="flex-1">
                  <div className="h-16 bg-gray-100 rounded-lg"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute -top-4 -right-4 w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
            <span className="text-2xl">⚙️</span>
          </div>
        </div>
      </div>
    </section>
  );
}
