"use client"
import React, { useState, useEffect } from "react";
import Image from "next/image";

export default function Navbar() {
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const controlNavbar = () => {
      if (typeof window !== 'undefined') {
        if (window.scrollY > lastScrollY && window.scrollY > 100) {
          // Scrolling down & past 100px
          setIsVisible(false);
        } else {
          // Scrolling up
          setIsVisible(true);
        }
        setLastScrollY(window.scrollY);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', controlNavbar);
      return () => {
        window.removeEventListener('scroll', controlNavbar);
      };
    }
  }, [lastScrollY]);

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ${
      isVisible ? 'translate-y-0' : '-translate-y-full'
    }`} style={{backgroundColor: 'var(--neutral-900)', borderBottom: '1px solid var(--neutral-800)'}}>
      <div className="max-w-7xl mx-auto flex items-center justify-between py-4 px-6">
        {/* Logo */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {/* Kambaa Logo */}
            <div className="relative">
              <div className="w-8 h-8 rounded-full" style={{backgroundColor: '#7DD3FC'}}></div>
              <div className="absolute top-1 left-2 w-6 h-6 rounded-full" style={{backgroundColor: '#A78BFA'}}></div>
            </div>
            <span className="font-bold text-xl" style={{color: 'var(--primary-600)'}}>Kambaa</span>
          </div>
        </div>

        {/* Navigation Links - Centered */}
        <div className="hidden md:flex items-center gap-8 absolute left-1/2 transform -translate-x-1/2">
          <a href="#" className="font-medium text-sm transition-colors duration-200 hover:text-primary-400" style={{color: 'var(--neutral-300)'}}>
            Home
          </a>
          <a href="#" className="font-medium text-sm transition-colors duration-200 hover:text-primary-400" style={{color: 'var(--neutral-300)'}}>
            About
          </a>
          <a href="#" className="font-medium text-sm transition-colors duration-200 hover:text-primary-400" style={{color: 'var(--neutral-300)'}}>
            Pricing
          </a>
        </div>

        {/* Sign In Button */}
        <div className="flex items-center">
          <button className="px-6 py-2 rounded-lg font-medium text-sm transition-all duration-200 hover:bg-primary-700" style={{backgroundColor: 'var(--primary-600)', color: 'white'}}>
            Sign In
          </button>
        </div>

        {/* Mobile menu button */}
        <button className="md:hidden" style={{color: 'var(--neutral-300)'}}>
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </nav>
  );
}