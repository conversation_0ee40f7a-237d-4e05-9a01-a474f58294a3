import React from "react";

export default function Navbar() {
  return (
    <nav className="w-full flex items-center justify-between py-6 px-8 bg-transparent">
      {/* Logo */}
      <div className="flex items-center gap-2 text-white font-semibold text-lg">
        <span className="inline-block w-6 h-6 bg-white rounded mr-2" style={{background: 'linear-gradient(135deg, #b0b6c1 0%, #23272a 100%)'}}></span>
        Flow
      </div>
      {/* Links */}
      <div className="hidden md:flex gap-8 text-gray-300 font-medium text-base">
        <a href="#" className="hover:text-white transition">Product</a>
        <a href="#" className="hover:text-white transition">Pricing</a>
        <a href="#" className="hover:text-white transition">Docs</a>
        <a href="#" className="hover:text-white transition">Blog</a>
      </div>
      {/* Buttons */}
      <div className="flex gap-3">
        <button className="px-5 py-2 rounded border border-gray-500 text-gray-200 hover:bg-gray-800 transition">Sign in</button>
        <button className="px-5 py-2 rounded bg-gradient-to-b from-gray-200 to-gray-400 text-black font-semibold hover:from-gray-300 hover:to-gray-500 transition">Install</button>
      </div>
    </nav>
  );
} 