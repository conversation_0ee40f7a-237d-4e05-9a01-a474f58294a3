import React from 'react';

export default function CTASection() {
  return (
    <section className="w-full py-24 px-4 bg-gradient-to-b from-transparent to-[#181d20]">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-[#23272a]/80 rounded-3xl p-12 border border-gray-700/50 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/10 to-purple-600/10"></div>
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
          
          <div className="relative z-10">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-100 mb-6">
              Ready to Transform Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">Voice</span>?
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join millions of users who trust our AI to convert their speech into perfect text. Start your free trial today.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <button className="px-10 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-bold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                Start Free Trial
              </button>
              <button className="px-10 py-4 border border-gray-600 text-gray-300 font-semibold rounded-full hover:bg-gray-800/50 transition-all duration-300">
                Schedule Demo
              </button>
            </div>
            
            <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>No credit card required</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>30-day money back</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}